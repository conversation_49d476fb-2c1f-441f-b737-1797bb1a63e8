﻿using System;
using KiotViet.Persistence.Common;
using KiotViet.Utilities;
using KiotViet.Web.Common;
using ServiceStack;
using ServiceStack.Caching;
using ServiceStack.Text;
using ServiceStack.Web;

namespace KiotViet.FnB.Public.Api.ServiceInterface.Filters
{
    public class RateLimitedAttribute : RequestFilterAttribute
    {
        public ICacheClient Cache { get; set; }
        public int RequestLimit { get; set; }
        public int RequestTimeIn { get; set; }

        public override void Execute(
            IRequest req, IResponse res, object requestDto)
        {
            if (Cache == null)
                Cache = req.TryResolve<ICacheClient>();

            RequestLimit = AppConfigInfo.RequestLimitApi;
            RequestTimeIn = AppConfigInfo.RequestTimeInSecondsApi;            
            var key = $"cache:ratelimit:{Globals.GetRetailerCode()}";
            var rateLimit = Cache.Get<RateLimit>(key);

            if (rateLimit != null && rateLimit.Remaining == 0)
            {
                var mess = $"Too Many Requests. You have exceeded your rate limit, it will be reset in {(rateLimit.Reset - DateTime.Now).TotalMinutes } minutes";
                throw WebUtil.GetError(ErrorCode.RateLimited, mess);
            }

            if (rateLimit == null)
            {
                //First request since reset
                rateLimit = new RateLimit
                {
                    Limit = RequestLimit,
                    Remaining = RequestLimit,
                    Reset = DateTime.Now.AddHours(RequestTimeIn)
                };
            }
            else
            {
                //Update existing record
                rateLimit.Remaining--;
            }
            var timespan = rateLimit.Reset > DateTime.Now ? rateLimit.Reset - DateTime.Now : TimeSpan.FromSeconds(RequestTimeIn);
            Cache.CacheSet(key, rateLimit, timespan);
            res.AddHeader("RateLimit", RequestLimit.ToString());
            res.AddHeader("Remaining", rateLimit.Remaining.ToString());
            res.AddHeader("Reset", rateLimit.Reset.ToUnixTime().ToString());
        }

    }

    public class RateLimit
    {
        public int Limit { get; set; }
        public int Remaining { get; set; }
        public DateTime Reset { get; set; }
    }
}