﻿using System;
using System.Runtime.Serialization;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/branches", "GET", Summary = "API lấy danh sách chi nhánh của cửa hàng", Notes = "API lấy danh sách chi nhánh của cửa hàng")]
    public class BranchList : PageReq, IReturn<SyncDataSources<BranchDTO>>
    {
       
        [ApiMember(Name = "includeRemoveIds", Description = "Có lấy thông tin danh sách Id bị xoá dựa trên lastModifiedFrom", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeRemoveIds")]
        public bool IncludeRemoveIds { get; set; }
    }
    [DataContract]
    public class BranchDTO
    {
        [DataMember(Name = "id")]
        public int Id { get; set; }
        [DataMember(Name = "branchName")]
        public string Name { get; set; }
        [DataMember(Name = "branchCode")]
        public string Code { get; set; }
        [DataMember(Name = "address")]
        public string Address { get; set; }
        [DataMember(Name = "locationName")]
        public string LocationName { get; set; }
        [DataMember(Name = "wardName")]
        public string WardName { get; set; }
        [DataMember(Name = "contactNumber")]
        public string ContactNumber { get; set; }
        [DataMember(Name= "retailerId")]
        public int RetailerId { get; set; }
        [DataMember(Name = "email")]
        public string Email { get; set; }
        [DataMember(Name= "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }
        [DataMember(Name = "createdDate")]
        public DateTime CreatedDate { get; set; }
    }
}
