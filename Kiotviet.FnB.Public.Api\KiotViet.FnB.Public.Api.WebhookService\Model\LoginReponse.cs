﻿using System.Runtime.Serialization;

namespace KiotViet.FnB.Public.Api.WebhookHelper.Model
{
    public class LoginReponse
    {
        [DataMember(Name = "access_token")]
        public string AccessToken { get; set; }

        [DataMember(Name = "token_type")]
        public string TokenType { get; set; }

        [DataMember(Name = "expires_in")]
        public int ExpiresIn { get; set; }

        [DataMember(Name = "userName")]
        public string UserName { get; set; }

    }
}
