﻿using System;
using KiotViet.Persistence.Common;
using KiotViet.RedisServices.Interface;
using KiotViet.Utilities;
using KiotViet.Web.Common;
using ServiceStack;
using ServiceStack.Web;

namespace KiotViet.Public.Api.ServiceInterface.Filters
{
    public class RetailerExpireFilterAttribute : RequestFilterAttribute
    {
        //This property will be resolved by the IoC container
        //public IRetailerService RetailerService { get; set; }
        public IGroupCacheService GroupCacheService { get; set; }
        public override void Execute(IRequest req, IResponse res, object requestDto)
        {
            var retailerCode = Globals.GetRetailerCode();
            if (!string.IsNullOrEmpty(retailerCode) && (retailerCode + ".").Equals(AppConfigInfo.SiteAdminFormat))
            {
                return;
            }
            var obj = GroupCacheService.GetById(retailerCode);
            if (obj?.RetailerObj.ExpiryDate != null && obj.RetailerObj.ExpiryDate.Value < DateTime.Now)
            {
                throw WebUtil.GetError(ErrorCode.RetailerExpired, string.Format(Resources.KVMessage.loginKiotExpired, obj.RetailerObj.ExpiryDate.GetValueOrDefault().ToString("dd/MM/yyyy")));
            }

        }
    }
}
