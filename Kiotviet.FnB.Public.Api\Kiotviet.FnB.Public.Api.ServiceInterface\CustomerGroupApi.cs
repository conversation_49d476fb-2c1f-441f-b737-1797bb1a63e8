﻿using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using KiotViet.Persistence;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Services.Interface;
using Linq2Rest;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
   public class CustomerGroupApi : BaseApi
    {
        public ICustomerGroupService CustomerGroupService { get; set; }

        public async Task<PagingDataSources<CustomerGroupDto>> Get(CustomerGroupList req)
        {
            var customers = CustomerGroupService.GetAll().AsNoTracking();
            customers = customers.OrderByDescending(c => c.Name);
            var temp = customers.Filter(req.GetModelFilter());
            var ls = await temp.Cast<CustomerGroup>().Take(req).ToListAsync();
            var result = new PagingDataSources<CustomerGroupDto>
            {
                Total = await temp.CountAsync(),
                Data = CustomerGroupService.DetachByClone(ls, new string[] { "CustomerGroupDetails" }).Map(x => x.ConvertTo<CustomerGroupDto>()).ToList()
        };
            return result;
        }
       
    }
}
