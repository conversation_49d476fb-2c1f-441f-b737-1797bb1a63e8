﻿using System.Collections.Generic;
using System.Linq;
using KiotViet.Persistence;
using KiotViet.Utilities;
using ServiceStack;
using ServiceStack.Caching;
using ServiceStack.Redis;

namespace KiotViet.Public.Api.ServiceInterface.Common
{
    public class NotificationSettingObject
    {
        public const string CacheKeyObjectFormat = "notificationSetting_{0}_{1}_{2}"; // notificationSetting_retailerId_eventType_userId
        public const string RetailerEventSetFormat = "notificationSettings_{0}_{1}"; // notificationSetting_retailerId_eventType
        public const string UserSetFormat = "notificationSettings_{0}"; // notificationSetting_userId

        public static void UpdateCache(int retailerId, long userId, IEnumerable<NotificationSetting> settings)
        {
            ICacheClient cacheClient = HostContext.TryResolve<ICacheClient>();
            IRedisClientsManager redisClientManager = HostContext.TryResolve<IRedisClientsManager>();

            // Update notification settings of user
            using (var redisClient = redisClientManager.GetClient())
            {
                var cacheUserSetKey = string.Format(UserSetFormat, userId);
                foreach (var setting in settings)
                {
                    var cacheObjectKey = string.Format(CacheKeyObjectFormat, retailerId, setting.EventType, userId);
                    if (cacheClient.Set(cacheObjectKey, setting))
                    {
                        // add object key into Retailer_EventType set
                        var cacheRetailerEventSetKey = string.Format(RetailerEventSetFormat, retailerId, setting.EventType);
                        redisClient.AddItemToSet(cacheRetailerEventSetKey, cacheObjectKey);

                        // add object key into User set
                        redisClient.AddItemToSet(cacheUserSetKey, cacheObjectKey);
                    }
                }

                // get notification settings remain are set false
                var defaultSettings = AppConfigInfo.DefaultNotificationSetting.Replace('\'', '"').FromJson<List<NotificationSetting>>();
                var removeEvents = defaultSettings.Where(x => !settings.Select(y => y.EventType).Contains(x.EventType)).Select(x => x.EventType);
                if (removeEvents.Any())
                {
                    foreach (var eventType in removeEvents)
                    {
                        var cacheObjectKey = string.Format(CacheKeyObjectFormat, retailerId, eventType, userId);

                        // remove object key from Retailer_EventType set
                        var cacheRetailerEventSetKey = string.Format(RetailerEventSetFormat, retailerId, eventType);
                        redisClient.RemoveItemFromSet(cacheRetailerEventSetKey, cacheObjectKey);

                        // remove object key from User set
                        redisClient.RemoveItemFromSet(cacheUserSetKey, cacheObjectKey);

                        // remove notification setting object
                        cacheClient.Remove(cacheObjectKey);
                    }
                }
            }
        }
    }
}
