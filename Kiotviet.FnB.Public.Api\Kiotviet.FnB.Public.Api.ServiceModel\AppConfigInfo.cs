﻿using KiotViet.Utilities;
using System;
using System.Configuration;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    public static class AppConfigInfo
    {

        public static string WebhookServer => ConfigurationManager.AppSettings["webhook_server"];
        public static string WebhookUserName => ConfigurationManager.AppSettings["webhook_username"];
        public static string WebhookPassword => ConfigurationManager.AppSettings["webhook_password"];
        public static int DefaultPageSize => Convert.ToInt32(ConfigurationManager.AppSettings["defaultPageSize"]);
        public static int MaxPageSize => Convert.ToInt32(ConfigurationManager.AppSettings["maxPageSize"]);
        public static int LimitPurchaseDateByDays => ConvertHelper.ToInt32(ConfigurationManager.AppSettings["LimitPurchaseDateByDays"], 7);
        public static int LimmitNumberBranchesQuerryOptimize => ConvertHelper.ToInt32(ConfigurationManager.AppSettings["LimmitNumberBranchesQuerryOptimize"], 20);
    }
}
