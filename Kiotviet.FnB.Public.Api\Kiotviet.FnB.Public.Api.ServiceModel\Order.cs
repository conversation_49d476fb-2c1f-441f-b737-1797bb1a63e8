﻿using KiotViet.Persistence;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Runtime.Serialization;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/orders", "GET", Summary = "API lấy danh sách đặt hàng",
    Notes = "API lấy danh sách đặt hàng")]
    public class GetListOrders : PageReq, IReturn<object>
    {
        [ApiMember(Name = "branchIds", Description = "Id chi nhánh",
        ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "branchIds")]
        public List<int> BranchIds { get; set; }
        [ApiMember(Name = "customerIds", Description = "Id khách hàng",
        ParameterType = "Query", DataType = "long", IsRequired = false)]
        [DataMember(Name = "customerIds")]
        public List<long> CustomerIds { get; set; }
        [ApiMember(Name = "status", Description = "Trạng thái đặt hàng",
        ParameterType = "Query", DataType = "int[]", IsRequired = false)]
        [DataMember(Name = "status")]
        public List<int> Status { get; set; }
        [ApiMember(Name = "customerCode", Description = "Mã khách hàng", ParameterType = "Query", DataType = "string", IsRequired = false)]
        [DataMember(Name = "customerCode")]
        public string CustomerCode { get; set; }

        [ApiMember(Name = "includePayment", Description = "Có lấy thông tinh danh sách Payments", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includePayment")]
        public bool IncludePayment { get; set; }
        [ApiMember(Name = "includeOrderDelivery", Description = "Có lấy thông tin danh sách Order Delivery", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeOrderDelivery")]
        public bool IncludeOrderDelivery { get; set; }
        [ApiMember(Name = "includeInvoices", Description = "Có lấy thông tin danh sách Hóa đơn", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeInvoices")]
        public bool IncludeInvoice { get; set; }
        [ApiMember(Name = "toDate", Description = "Thời gian thay đổi dữ liệu lần cuối cho đến ngày toDate (nhằm hỗ trợ đồng bộ dữ liệu dựa theo TimeStamp)", DataType = "DateTime", IsRequired = false)]
        [DataMember(Name = "toDate")]
        public DateTime? ToDate { get; set; }
        [ApiMember(Name = "createdDate", Description = "Lấy danh sách đặt hàng theo ngày tạo", DataType = "DateTime", IsRequired = false)]
        [DataMember(Name = "createdDate")]
        public DateTime? CreatedDate { get; set; }
    }

    [Route("/orders/{Id}", "GET", Summary = "API lấy thông tin đơn đặt hàng", Notes = "API lấy thông tin đơn đặt hàng theo Id")]
    [Route("/orders/code/{code}", "GET", Summary = "API lấy thông tin đơn đặt hàng", Notes = "API lấy thông tin đơn đặt hàng theo Code")]
    public class GetOrder : IReturn<OrderPublicDTO>
    {
        [ApiMember(Name = "Id", Description = "Id đặt hàng", ParameterType = "Path", DataType = "long", IsRequired = true)]
        public long Id { get; set; }

        [ApiMember(Name = "Code", Description = "Mã đặt hàng", ParameterType = "Path", DataType = "string", IsRequired = true)]
        public string Code { get; set; }
    }

    [Route("/orders", "POST", Summary = "API thêm mới đơn đặt hàng",
    Notes = "API thêm mới hoặc cập nhật đơn đặt hàng")]
    [DataContract]
    public class CreateOrder : OrderRequest, IReturn<OrderDTO>
    {


    }
    [Route("/orders/{Id}", "PUT", Summary = "API cập nhật đơn đặt hàng",
    Notes = "API thêm mới hoặc cập nhật đơn đặt hàng")]
    [DataContract]
    public class UpdateOrder : OrderRequest, IReturn<OrderDTO>
    {

    }

    [Route("/orders/{Id}", "DELETE", Summary = "API xóa đặt hàng",
    Notes = "API xóa đặt hàng")]
    public class VoidOrder : IReturn<object>
    {
        [ApiMember(Name = "Id", Description = "Id đơn đặt hàng",
        ParameterType = "Path", DataType = "long", IsRequired = true)]
        public long Id { get; set; }

        [ApiMember(Name = "IsVoidPayment", Description = "Có xóa phiếu thanh toán đi kèm đơn đặt hàng không",
        ParameterType = "Path", DataType = "bool", IsRequired = true)]
        public bool IsVoidPayment { get; set; }
    }
    [DataContract]
    public class OrderRequest : OrderDTO
    {
        [DataMember(Name = "cashierId")]
        public long? CashierId { get; set; }

        [DataMember(Name = "method")]
        public string Method { get; set; }

        [DataMember(Name = "accountId")]
        public int? AccountId { get; set; }
        [DataMember(Name = "makeInvoice")]
        public bool MakeInvoice { get; set; }

        [DataMember(Name = "customer")]
        public CustomerDTO Customer { get; set; }
        [DataMember(Name = "surchages")]
        public List<SurchagesAddDto> Surchages { get; set; }

    }
    [DataContract]
    public class OrderPublicDTO : OrderDTO
    {
        [DataMember(Name = "invoices", Order = 24)]
        public List<InvoicesByOrderDTO> Invoices { get; set; }
        [DataMember(Name = "invoiceOrderSurcharges", Order = 25)]
        public List<InvoiceOrderSurchargeDto> InvoiceOrderSurcharges { get; set; }
    }
    [DataContract]
    public class InvoicesByOrderDTO
    {
        [DataMember(Name = "invoiceId")]
        public long Id { get; set; }
        [DataMember(Name = "invoiceCode")]
        public string Code { get; set; }
        [DataMember(Name = "total")]
        public decimal Total { get; set; }
        [DataMember(Name = "status")]
        public byte Status { get; set; }
        [DataMember(Name = "discount")]
        public decimal? Discount { get; set; }
        [DataMember(Name = "discountRatio")]
        public double? DiscountRatio { get; set; }
    }

    [Route("/orders", "POST", Summary = "API đặt hàng")]
    public class OrderRequestDTO : IReturn<OrderResponseDTO>
    {
        [DataMember(Name = "retailerId")]
        [ApiMember(Name = "retailerId", Description = "Mã cửa hàng", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int RetailerId { get; set; }

        [DataMember(Name = "branchId")]
        [ApiMember(Name = "branchId", Description = "Mã chi nhánh", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int BranchId { get; set; }

        [DataMember(Name = "soldById")]
        [ApiMember(Name = "soldById", Description = "Mã nhân viên bán hàng", ParameterType = "Query", DataType = "long?", IsRequired = false)]
        public long? SoldById { get; set; }

        [DataMember(Name = "purchaseDate")]
        [ApiMember(Name = "purchaseDate", Description = "Ngày đặt hàng", ParameterType = "Query", DataType = "DateTime", IsRequired = true)]
        public DateTime PurchaseDate { get; set; }

        [DataMember(Name = "customerId")]
        [ApiMember(Name = "customerId", Description = "Mã khách hàng", ParameterType = "Query", DataType = "long?", IsRequired = false)]
        public long? CustomerId { get; set; }

        [DataMember(Name = "orderDetails")]
        [ApiMember(Name = "orderDetails", Description = "Danh sách đặt hàng", ParameterType = "Query", DataType = "List<OrderDetailDTO>", IsRequired = true)]
        public List<OrderDetailRequestDTO> OrderDetails { get; set; }

        [DataMember(Name = "deliveryDetail")]
        [ApiMember(Name = "deliveryDetail", Description = "Địa chỉ giao hàng", ParameterType = "Query", DataType = "OrderDeliveryDTO", IsRequired = false)]
        public OrderDeliveryRequestDTO DeliveryDetail { get; set; }

        [DataMember(Name = "usingCod")]
        [ApiMember(Name = "usingCod", Description = "Có giao hàng không", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int UsingCod { get; set; }

        [DataMember(Name = "diningOption")]
        [ApiMember(Name = "diningOption", Description = "Loại đặt hàng", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int DiningOption { get; set; }

        [DataMember(Name = "Extra")]
        public string Extra { get; set; }

        [DataMember(Name = "uuid")]
        public string Uuid { get; set; }
    }


    [Route("/orders/create", "POST", Summary = "API đặt hàng")]
    public class CreateOrderRequestDto : IReturn<OrderResponseDTO>
    {
        [DataMember(Name = "retailerId")]
        [ApiMember(Name = "retailerId", Description = "Mã cửa hàng", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int RetailerId { get; set; }

        [DataMember(Name = "branchId")]
        [ApiMember(Name = "branchId", Description = "Mã chi nhánh", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int BranchId { get; set; }

        [DataMember(Name = "soldById")]
        [ApiMember(Name = "soldById", Description = "Mã nhân viên bán hàng", ParameterType = "Query", DataType = "long?", IsRequired = false)]
        public long? SoldById { get; set; }

        [DataMember(Name = "purchaseDate")]
        [ApiMember(Name = "purchaseDate", Description = "Ngày đặt hàng", ParameterType = "Query", DataType = "DateTime", IsRequired = true)]
        public DateTime PurchaseDate { get; set; }

        [DataMember(Name = "customerId")]
        [ApiMember(Name = "customerId", Description = "Mã khách hàng", ParameterType = "Query", DataType = "long?", IsRequired = false)]
        public long? CustomerId { get; set; }

        [DataMember(Name = "orderDetails")]
        [ApiMember(Name = "orderDetails", Description = "Danh sách đặt hàng", ParameterType = "Query", DataType = "List<OrderDetailDTO>", IsRequired = true)]
        public List<OrderDetailRequestDTO> OrderDetails { get; set; }

        [DataMember(Name = "deliveryDetail")]
        [ApiMember(Name = "deliveryDetail", Description = "Địa chỉ giao hàng", ParameterType = "Query", DataType = "OrderDeliveryDTO", IsRequired = false)]
        public OrderDeliveryRequestDTO DeliveryDetail { get; set; }

        [DataMember(Name = "usingCod")]
        [ApiMember(Name = "usingCod", Description = "Có giao hàng không", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int UsingCod { get; set; }

        [DataMember(Name = "diningOption")]
        [ApiMember(Name = "diningOption", Description = "Loại đặt hàng", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int DiningOption { get; set; }

        [DataMember(Name = "Extra")]
        public string Extra { get; set; }

        [DataMember(Name = "uuid")]
        public string Uuid { get; set; }
    }


    [Route("/orders/uuid/{Uuid}", "GET", Summary = "API lấy thông tin đơn đặt hàng", Notes = "API lấy thông tin đơn đặt hàng theo Uuid")]
    public class GetOrderByUuid : IReturn<OrderResponseDTO>
    {
        [DataMember(Name = "Uuid")]
        [ApiMember(Name = "Uuid", Description = "Uuid Order", ParameterType = "Query", DataType = "string", IsRequired = true)]
        public string Uuid { get; set; }
    }

    public class OrderDeliveryRequestDTO
    {
        [DataMember(Name = "receiver")]
        [ApiMember(Name = "receiver", Description = "Người nhận hàng", ParameterType = "Query", DataType = "string", IsRequired = true)]
        public string Receiver { get; set; }

        [DataMember(Name = "contactNumber")]
        [ApiMember(Name = "contactNumber", Description = "Số điện thoại người nhận hàng", ParameterType = "Query", DataType = "string", IsRequired = true)]
        public string ContactNumber { get; set; }

        [DataMember(Name = "address")]
        [ApiMember(Name = "address", Description = "Địa chỉ nhận hàng", ParameterType = "Query", DataType = "string", IsRequired = true)]
        public string Address { get; set; }

        [DataMember(Name = "administrativeAreaId")]
        [ApiMember(Name = "administrativeAreaId", Description = "ID phường xã nhận hàng", ParameterType = "Query", DataType = "int?", IsRequired = true)]
        public int AdministrativeAreaId { get; set; }

        [DataMember(Name = "locationName")]
        [ApiMember(Name = "locationName", Description = "Tỉnh thành nhận hàng", ParameterType = "Query", DataType = "string", IsRequired = true)]
        public string LocationName { get; set; }

        [DataMember(Name = "wardName")]
        [ApiMember(Name = "wardName", Description = "Phường xã nhận hàng", ParameterType = "Query", DataType = "string", IsRequired = true)]
        public string WardName { get; set; }

        [DataMember(Name = "deliveryBy")]
        [ApiMember(Name = "deliveryBy", Description = "ID đối tác giao hàng", ParameterType = "Query", DataType = "long?", IsRequired = false)]
        public long? DeliveryBy { get; set; }

        [DataMember(Name = "deliveryCode")]
        [ApiMember(Name = "deliveryCode", Description = "Mã vận đơn", ParameterType = "Query", DataType = "string", IsRequired = false)]
        public string DeliveryCode { get; set; }

        [DataMember(Name = "partnerDelivery")]
        [ApiMember(Name = "partnerDelivery", Description = "Đối tác giao hàng", ParameterType = "Query", DataType = "PartnerDeliveryDTO", IsRequired = false)]
        public PartnerDeliveryDTO PartnerDelivery { get; set; }

        [DataMember(Name = "expectedDelivery")]
        [ApiMember(Name = "expectedDelivery", Description = "Ngày dự kiến giao hàng", ParameterType = "Query", DataType = "DateTime?", IsRequired = false)]
        public DateTime? ExpectedDelivery { get; set; }

        [DataMember(Name = "status")]
        [ApiMember(Name = "status", Description = "Trạng thái giao hàng", ParameterType = "Query", DataType = "int?", IsRequired = false)]
        public int? Status { get; set; }

        [DataMember(Name = "price")]
        [ApiMember(Name = "price", Description = "Phí giao hàng", ParameterType = "Query", DataType = "decimal?", IsRequired = false)]
        public decimal? Price { get; set; }
    }

    public class OrderDetailRequestDTO
    {
        [DataMember(Name = "uuid")]
        [ApiMember(Name = "uuid", Description = "Uuid sản phẩm", ParameterType = "Query", DataType = "string", IsRequired = true)]
        public string Uuid { get; set; }

        [DataMember(Name = "productId")]
        [ApiMember(Name = "productId", Description = "ID sản phẩm", ParameterType = "Query", DataType = "long", IsRequired = true)]
        public long ProductId { get; set; }

        [DataMember(Name = "price")]
        [ApiMember(Name = "price", Description = "Giá bán", ParameterType = "Query", DataType = "decimal", IsRequired = true)]
        public decimal Price { get; set; }

        [DataMember(Name = "quantity")]
        [ApiMember(Name = "quantity", Description = "Số lượng", ParameterType = "Query", DataType = "double", IsRequired = true)]
        public double Quantity { get; set; }

        [DataMember(Name = "note")]
        [ApiMember(Name = "note", Description = "Ghi chú", ParameterType = "Query", DataType = "string", IsRequired = false)]
        public string Note { get; set; }

        [DataMember(Name = "isStartTimeCounter")]
        [ApiMember(Name = "isStartTimeCounter", Description = "Có là hàng tính giờ không?", ParameterType = "Query", DataType = "bool?", IsRequired = false)]
        public bool? IsStartTimeCounter { get; set; }


        [DataMember(Name = "timeStartCounter")]
        [ApiMember(Name = "timeStartCounter", Description = "Thời gian bắt đầu tính giờ", ParameterType = "Query", DataType = "DateTime?", IsRequired = false)]
        public DateTime? TimeStartCounter { get; set; }

        [DataMember(Name = "timeEndCounter")]
        [ApiMember(Name = "timeEndCounter", Description = "Thời gian kết thúc tính giờ", ParameterType = "Query", DataType = "DateTime?", IsRequired = false)]
        public DateTime? TimeEndCounter { get; set; }

        //[DataMember(Name = "rank")]
        //[ApiMember(Name = "rank", Description = "Xếp hạng", ParameterType = "Query", DataType = "int?", IsRequired = false)]
        //public int? Rank { get; set; }

    }

    public class OrderDetailResponseDTO : OrderDetailRequestDTO
    {

    }

    public class OrderDeliveryResponseDTO : OrderDeliveryRequestDTO
    {

    }


    public class OrderResponseDTO
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public DateTime PurchaseDate { get; set; }
        public int RetailerId { get; set; }
        public int BranchId { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; }
        public long? SoldById { get; set; }
        public List<OrderDetailResponseDTO> OrderDetails { get; set; }
        public OrderDeliveryResponseDTO OrderDelivery { get; set; }
        public int Status { get; set; }
        public string StatusValue { get; set; }
        public double ToTalQuantity { get; set; }
        public decimal Total { get; set; }
        public decimal TotalPayment { get; set; }
        public string HistoryNote { get; set; }     
        public int DiningOption { get; set; }
        public int? UsingCod { get; set; }
    }

    public class OrderExtraJson
    {
        public object Amount { get; set; }
        public ExpandoObject Method { get; set; }
        public object AccountId { get; set; }
        public PriceBook PriceBookId { get; set; }
        public DateTime EntryDate { get; set; }
        public long?[] GroupedTableIds { get; set; }
    }
}