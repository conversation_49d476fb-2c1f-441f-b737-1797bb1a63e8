﻿using KiotViet.Persistence;
using System;
using System.Collections.Generic;

namespace KiotViet.Api.ServiceModel
{
    public class SyncResult
    {
        public long Id { get; set; }
        public long? LocalId { get; set; }
        //public string Code { get; set; }

        public string Error { get; set; }
        //public DateTime CreatedDate { get; set; }
        //public DateTime? ModifiedDate { get; set; }
        public object Entity { get; set; }


    }

    public class ValidResult
    {
        public Order Order { get; set; }
        public string Message { get; set; }
    }

    public class SyncSorter : IComparer<DateTime>
    {
        public int Compare(DateTime x, DateTime y)
        {
            if (x == default(DateTime) && y == default(DateTime))
                return 0;
            else if (x == default(DateTime))
                return 1;
            else if (y == default(DateTime))
                return -1;
            else
                return x.CompareTo(y);
        }
    }
}
