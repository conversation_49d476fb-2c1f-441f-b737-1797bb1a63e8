﻿using KiotViet.Api.ServiceModel;
using KiotViet.Persistence;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/partnerdeliveries", "GET", Summary = "API lấy  danh sách đối tác giao hàng",
       Notes = "API lấy danh sách đối tác giao hàng")]
    public class PartnerDeliveryList : PageReq, IReturn<SyncDataSources<PartnerDelivery>>
    {

        [ApiMember(Name = "ModifiedDate", Description = "Thời gian thay đổi dữ liệu lần cuối (nhằm hỗ trợ đồng bộ dữ liệu dựa theo timeStamp)",
        ParameterType = "Query", DataType = "Date", IsRequired = false)]
        public DateTime? ModifiedDate { get; set; }

        [ApiMember(Name = "FindString", Description = "Lọc theo tên và số điện thoại đối tác giao hàng",
            ParameterType = "Query", DataType = "string", IsRequired = false)]
        public string FindString { get; set; }

        [ApiMember(Name = "IncludeRemoved", Description = "Có lấy danh sách Id các đối tác giao hàng đã bị xóa dựa theo ModifiedDate hay không (nhằm hỗ trợ đồng bộ dữ liệu dựa theo timeStamp)",
        ParameterType = "Query", DataType = "Date", IsRequired = false)]
        public bool? IncludeRemoved { get; set; }
        [ApiMember(Name = "IsActive", DataType = "bool?", Description = @"Trạng thái: true là hoạt động, false là ngừng hoạt động", ParameterType = "Query", IsRequired = false)]
        public bool? IsActive { get; set; }
    }

    [Route("/partnerdeliveries", "POST", Summary = "API Thêm mới hoặc cập nhật thông tin cho đối tác giao hàng",
    Notes = "API Thêm mới hoặc cập nhật thông tin cho đối tác giao hàng")]
    public class PartnerDeliveryCreateOrUpdate : IReturn<object>
    {
        public PartnerDeliveryMobileDTO PartnerDelivery { get; set; }
    }

    [Route("/partnerdeliveries/sync", "POST")]
    public class PartnerDeliverySync : IReturn<IList<SyncResult>>
    {
        public IList<PartnerDeliveryMobileDTO> PartnerDeliveries { get; set; }
    }

    public class PartnerDeliveryMobileDTO
    {
        public long Id { get; set; }
        public byte Type { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string ContactNumber { get; set; }
        public string Comments { get; set; }
        public string LocationName { get; set; }
        public string WardName { get; set; }
        public int? LocationId { get; set; }
        public string Address { get; set; }
        public string Uuid { get; set; }
        public bool? isDeleted { get; set; }
        public bool? isActive { get; set; }

        public DateTime? ModifiedDate { get; set; }
        public List<PartnerDeliveryGroupDetailDTO> PartnerDeliveryGroupDetails { get; set; }
    }

    public class PartnerDeliveryGroupDetailDTO
    {
        public int GroupId { get; set; }
        public int Id { get; set; }
        public long PartnerDeliveryId { get; set; }
    }

    public class PartnerDeliveryDto
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string ContactNumber { get; set; }
        public string Address { get; set; }
        public string Email { get; set; }
    }
}
