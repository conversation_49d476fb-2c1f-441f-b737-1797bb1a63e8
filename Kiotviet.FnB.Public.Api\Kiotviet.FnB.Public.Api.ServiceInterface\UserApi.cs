﻿using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using KiotViet.Persistence;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Services.Interface;
using Linq2Rest;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class UserApi : BaseApi
    {
        public IUserService UserService { get; set; }
        public IShadowService ShadowService { get; set; }

        public async Task<SyncDataSources<UserDTO>> Get(UserList req)
        {
            var result = new SyncDataSources<UserDTO>();
            var baseUserList = UserService.GetAll().Where(p => p.IsActive && !p.IsAdmin && !(p.isDeleted ?? false)).AsNoTracking();
            if (req.LastModifiedFrom != null)
            {
                baseUserList = baseUserList.Where(p => p.CreatedDate >= req.LastModifiedFrom);
            }
            var userList = baseUserList.Select(p => new UserDTO()
            {
                Id = p.Id,
                UserName = p.UserName,
                GivenName = p.GivenName,
                Address = p.Address,
                MobilePhone = p.MobilePhone,
                Email = p.Email,
                RetailerId = p.RetailerId,
                BirthDate = p.DOB,
                Description = p.Note,
                CreatedDate = p.CreatedDate
            });
            result.Total = await userList.CountAsync();
            if (req.Orderby == null) userList = userList.OrderBy(p => p.CreatedDate);
            var lst = userList.Filter(req.GetModelFilter());
            lst = lst.Take(req);
            result.Data = (await lst.ToListAsync()).Select(x => x.ConvertTo<UserDTO>()).ToList();
            result.PageSize = GetPageSize(req.PageSize);
            if (req.IncludeRemoveIds)
            {
                var shadowTemp =
                    await ShadowService.GetDeleted(typeof(User).Name, CurrentRetailerId, req.LastModifiedFrom)
                        .Where(p => p.PeggedId == CurrentRetailerId)
                        .Select(t => t.Id).ToListAsync();
                var notActiveAndIsDeletedLst = UserService.GetAll().Where(p => !p.IsActive && !p.IsAdmin && (p.isDeleted ?? false) && p.CreatedDate >= req.LastModifiedFrom).Select(t => t.Id);
                if (notActiveAndIsDeletedLst.Any())
                    shadowTemp = shadowTemp.Union(notActiveAndIsDeletedLst).ToList();
                result.RemovedIds = shadowTemp;
            }
            return result;
        }
    }
}
