﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{08BEF328-AB7D-48D3-800D-48ACC75C7914}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>KiotViet.FnB.Public.Api.ServiceModel</RootNamespace>
    <AssemblyName>KiotViet.FnB.Public.Api.ServiceModel</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="KiotViet.Exceptions">
      <HintPath>..\..\Component\KiotViet.Exceptions.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.MongoDb, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.MongoDb.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Persistence, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.Persistence.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Services, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.Services.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Utilities, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Interfaces.5.4.0\lib\net45\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppConfigInfo.cs" />
    <Compile Include="Branch.cs" />
    <Compile Include="Category.cs" />
    <Compile Include="Common.cs" />
    <Compile Include="Customer.cs" />
    <Compile Include="CustomerGroup.cs" />
    <Compile Include="Invoice.cs" />
    <Compile Include="Order.cs" />
    <Compile Include="OrderByModelExtension.cs" />
    <Compile Include="PageReq.cs" />
    <Compile Include="PagingDataSources.cs" />
    <Compile Include="PartnerDelivery.cs" />
    <Compile Include="Product.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Surchages.cs" />
    <Compile Include="TakeModelExtension.cs" />
    <Compile Include="WebhookDto.cs" />
    <Compile Include="User.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Types\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(SignAssembly)'=='Release|AnyCPU|True'">
    <PostBuildEvent>kvfnb-sign $(TargetPath)</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>