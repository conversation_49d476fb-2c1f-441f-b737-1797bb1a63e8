﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    public static class OrderByModelExtension
    {
        /// <summary>
        /// OrderBy: key1,-key2:,-key3
        /// <br/>
        /// Source: http://stackoverflow.com/a/31959568
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="orderString"></param>
        /// <returns></returns>
        public static IQueryable<T> OrderBy<T>(this IQueryable<T> source, string orderString)
        {
            IList<OrderByStatement> statements = ParseOrderString(orderString);
            var entityType = typeof(T);
            bool isReadyOrder = false;
            foreach (var ord in statements.Reverse())
            {
                //Create x=>x.PropName
                var propertyInfo = entityType.GetProperty(ord.FieldName);
                if (propertyInfo == null)
                    throw new Exception("Invalid OrderBy: " + ord.FieldName);
                ParameterExpression arg = Expression.Parameter(entityType, "x");
                MemberExpression property = Expression.Property(arg, ord.FieldName);
                var selector = Expression.Lambda(property, new ParameterExpression[] { arg });

                // Get System.Linq.Queryable.OrderBy() method.
                var enumarableType = typeof(System.Linq.Queryable);
                var methodName = isReadyOrder ? ord.IsDescrease ? "ThenByDescending" : "ThenBy" : ord.IsDescrease ? "OrderByDescending" : "OrderBy";
                var method = enumarableType.GetMethods()
                     .Where(m => m.Name == methodName && m.IsGenericMethodDefinition)
                     .Where(m =>
                     {
                         var parameters = m.GetParameters().ToList();
                         //Put more restriction here to ensure selecting the right overload                
                         return parameters.Count == 2;//overload that has 2 parameters
                     }).Single();
                //The linq's OrderBy<TSource, TKey> has two generic types, which provided here
                MethodInfo genericMethod = method
                     .MakeGenericMethod(entityType, propertyInfo.PropertyType);

                /*Call query.OrderBy(selector), with query and selector: x=> x.PropName
                  Note that we pass the selector as Expression to the method and we don't compile it.
                  By doing so EF can extract "order by" columns and generate SQL for it.*/
                source = (IOrderedQueryable<T>)genericMethod
                     .Invoke(genericMethod, new object[] { source, selector });
            }
            return source;
        }

        private static IList<OrderByStatement> ParseOrderString(string orderString)
        {
            List<OrderByStatement> statements = new List<OrderByStatement>();
            var orderSplited = orderString.Split(',').Select(o => o.Trim());
            foreach(var s in orderSplited)
            {
                if(s[0] == '-')
                {
                    statements.Add(new OrderByStatement() { FieldName = s.Substring(1), IsDescrease = true });
                }
                else
                {
                    statements.Add(new OrderByStatement() { FieldName = s, IsDescrease = false });
                }
            }
            return statements;
        }

        class OrderByStatement
        {
            public string FieldName { get; set; }
            public bool IsDescrease { get; set; }
        }
    }
}
