﻿using ServiceStack;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/categories", "GET", Summary = "API lấy danh sách nhóm hàng hóa", Notes = "API lấy danh sách nhóm hàng hóa")]
    public class CategoryList : PageReq, IReturn<SyncDataSources<CategoriesDTO>>
    {
        [ApiMember(Name = "includeRemoveIds", Description = "Có lấy thông tin danh sách Id bị xoá dựa trên lastModifiedFrom", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeRemoveIds")]
        public bool IncludeRemoveIds { get; set; }

        [ApiMember(Name = "hierachicalData", Description = "HierachicalData = true lấy danh sách nhóm hàng theo cấp, HierachicalData = false lấy danh sách nhóm hàng theo lastModifiedFrom")]
        [DataMember(Name = "hierachicalData")]
        public bool HierachicalData { get; set; }
    }

    [Route("/categories/{id}", "DELETE", Summary = "API xóa nhóm hàng hóa", Notes = "API xóa nhóm hàng hóa")]
    public class CategoryDelete : IReturn<object>
    {
        [ApiMember(Name = "id", Description = "Id nhóm hàng cần xóa", ParameterType = "Path", DataType = "int", IsRequired = true)]
        [DataMember(Name = "id")]
        public int Id { get; set; }
    }

    [Route("/categories/{id}", "GET", Summary = "API lấy thông tin chi tiết nhóm hàng", Notes = "API lấy thông tin chi tiết nhóm hàng")]
    public class CategoryDetail : IReturn<CategoriesDTO>
    {
        [ApiMember(Name = "id", Description = "Id nhóm hàng cần lấy thông tin chi tiết", ParameterType = "Path", DataType = "int", IsRequired = true)]
        [DataMember(Name = "id")]
        public int Id { get; set; }
    }

    [Route("/categories", "POST", Summary = "API thêm mới hoặc cập nhật nhóm hàng", Notes = "API thêm mới hoặc cập nhật nhóm hàng")]
    [DataContract]
    public class CategoryCreate : CategoryRequest, IReturn<object>
    {
    }
    [Route("/categories/{id}", "PUT", Summary = "API cập nhật nhóm hàng", Notes = "API cập nhật nhóm hàng")]
    [DataContract]
    public class CategoryUpdate : CategoryRequest, IReturn<object>
    {
        [DataMember(Name = "id")]
        [ApiMember(Name = "id", Description = "id khách hàng", ParameterType = "Path", DataType = "int", IsRequired = true)]
        public int Id { get; set; }
    }
    [DataContract]
    public class CategoryRequest
    {
        [ApiMember(Name = "categoryName", Description = "Tên khách hàng", ParameterType = "Query", DataType = "string", IsRequired = true)]
        [DataMember(Name = "categoryName")]
        public string Name { get; set; }
        [ApiMember(Name = "parentId", Description = "Nhóm hàng cha (Nếu có)", ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "parentId")]
        public int? ParentId { get; set; }
    }
    [DataContract]
    public class CategoriesDTO
    {
        [DataMember(Name = "categoryId")]
        public int Id { get; set; }
        [DataMember(Name = "parentId")]
        public int? ParentId { get; set; }
        [DataMember(Name = "categoryName")]
        public string Name { get; set; }
        [DataMember(Name = "retailerId")]
        public int RetailerId { get; set; }
        [DataMember(Name = "hasChild")]
        public bool? HasChild { get; set; }
        [DataMember(Name = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }
        [DataMember(Name = "createdDate")]
        public DateTime CreatedDate { get; set; }
        [DataMember(Name = "children")]
        public IEnumerable<CategoriesDTO> Children { get; set; }
    }
}
