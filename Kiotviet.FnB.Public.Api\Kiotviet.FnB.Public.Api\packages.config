﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Autofac" version="4.6.1" targetFramework="net462" />
  <package id="Autofac.Mvc5" version="4.0.2" targetFramework="net462" />
  <package id="Confluent.Kafka" version="1.1.0" targetFramework="net462" />
  <package id="Consul" version="1.7.14.2" targetFramework="net462" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net462" />
  <package id="EntityFramework.BulkInsert-ef6" version="6.0.2.8" targetFramework="net462" />
  <package id="EntityFramework.MappingAPI" version="6.0.0.7" targetFramework="net462" />
  <package id="KiotVietFnB.BuildInfoTracing" version="1.0.0" targetFramework="net462" />
  <package id="KiotVietFnB.BuildInfoTracing.ServiceStackApi" version="1.0.0" targetFramework="net462" />
  <package id="KiotVietFnB.EntityFramework.Extensions" version="1.2.4" targetFramework="net462" />
  <package id="KiotVietFnB.EntityFramework.Extensions.SqlServer" version="1.2.4" targetFramework="net462" />
  <package id="KvFnBConsul" version="1.3.0" targetFramework="net462" />
  <package id="librdkafka.redist" version="1.1.0" targetFramework="net462" />
  <package id="LINQKit" version="1.1.2" targetFramework="net462" />
  <package id="log4net" version="2.0.8" targetFramework="net462" />
  <package id="Mehdime.Entity" version="1.0.0" targetFramework="net462" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net462" />
  <package id="Microsoft.AspNet.Razor" version="3.2.6" targetFramework="net462" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net462" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net462" />
  <package id="Microsoft.Owin" version="3.0.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="3.0.0" targetFramework="net462" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net462" />
  <package id="MongoDB.Bson" version="2.2.4" targetFramework="net462" />
  <package id="MongoDB.Driver" version="2.2.4" targetFramework="net462" />
  <package id="MongoDB.Driver.Core" version="2.2.4" targetFramework="net462" />
  <package id="MSBuildTasks" version="*********" targetFramework="net462" developmentDependency="true" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net462" />
  <package id="Owin" version="1.0" targetFramework="net462" />
  <package id="RabbitMQ.Client" version="3.6.9" targetFramework="net462" />
  <package id="RustFlakes" version="2.0.0" targetFramework="net462" />
  <package id="Serilog" version="2.10.0" targetFramework="net462" />
  <package id="Serilog.Enrichers.Environment" version="2.1.2" targetFramework="net462" />
  <package id="Serilog.Enrichers.HttpContextData" version="0.1.2" targetFramework="net462" />
  <package id="Serilog.Enrichers.Memory" version="1.0.4" targetFramework="net462" />
  <package id="Serilog.Enrichers.Process" version="2.0.1" targetFramework="net462" />
  <package id="Serilog.Enrichers.Thread" version="3.0.0" targetFramework="net462" />
  <package id="Serilog.Formatting.Compact" version="1.0.0" targetFramework="net462" />
  <package id="Serilog.Settings.AppSettings" version="2.1.2" targetFramework="net462" />
  <package id="Serilog.Sinks.Async" version="1.3.0" targetFramework="net462" />
  <package id="Serilog.Sinks.File" version="4.0.0" targetFramework="net462" />
  <package id="Serilog.Sinks.PeriodicBatching" version="2.1.1" targetFramework="net462" />
  <package id="Serilog.Sinks.RollingFile" version="3.3.0" targetFramework="net462" />
  <package id="Serilog.Sinks.Seq" version="3.4.0" targetFramework="net462" />
  <package id="ServiceStack" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Api.Swagger" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Client" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Common" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Interfaces" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Logging.Log4Net" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Logging.Serilog" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.OrmLite" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Razor" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Redis" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Request.Correlation" version="5.0.0" targetFramework="net462" />
  <package id="ServiceStack.Server" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Text" version="5.4.0" targetFramework="net462" />
  <package id="Superpower" version="1.1.0" targetFramework="net462" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net462" />
  <package id="System.Configuration.ConfigurationManager" version="4.7.0" targetFramework="net462" />
  <package id="System.Memory" version="4.5.1" targetFramework="net462" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net462" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.0" targetFramework="net462" />
  <package id="System.Security.AccessControl" version="4.7.0" targetFramework="net462" />
  <package id="System.Security.Permissions" version="4.7.0" targetFramework="net462" />
  <package id="System.Security.Principal.Windows" version="4.7.0" targetFramework="net462" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
  <package id="Z.EntityFramework.Extensions" version="3.12.14" targetFramework="net462" />
</packages>