﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using KiotViet.FnB.Public.Api.WebhookHelper;

namespace TestServices
{
    class Program
    {
        static void Main(string[] args)
        {
            var obj =new List<Datum>()
            {
                new Datum()
                {
                    Id = 80697,
                    Code = "DH000215",
                    BranchId = 4,
                    BranchName = "Hai Bà Trưng",
                    SoldById = 31,
                    OrderDetails = new List<OrderDetail>
                    {
                        new OrderDetail()
                        {
                            ProductId = 46,
                            ProductCode = "SP000021"
                        }
                    }
                }
            };

            var task = Task.Factory.StartNew(async () => { await WebhookServices.Notify("product.update", 149, obj); });
            task.Wait();
          
            Console.ReadLine();

        }
    }
    public class OrderDetail
    {
        public int ProductId { get; set; }
        public string ProductCode { get; set; }
        public string ProductName { get; set; }
        public int Quantity { get; set; }
        public int Price { get; set; }
        public int Discount { get; set; }
        public int DiscountRatio { get; set; }
    }

    public class PartnerDelivery
    {
        public string Code { get; set; }
        public string Name { get; set; }
    }

    public class OrderDelivery
    {
        public string DeliveryCode { get; set; }
        public string Receiver { get; set; }
        public string ContactNumber { get; set; }
        public string Address { get; set; }
        public int Weight { get; set; }
        public int Length { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public int PartnerDeliveryId { get; set; }
        public PartnerDelivery PartnerDelivery { get; set; }
    }

    public class Datum
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string PurchaseDate { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; }
        public int SoldById { get; set; }
        public string SoldByName { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; }
        public int Total { get; set; }
        public int TotalPayment { get; set; }
        public int Discount { get; set; }
        public int DiscountRatio { get; set; }
        public int Status { get; set; }
        public string StatusValue { get; set; }
        public bool UsingCod { get; set; }
        public List<OrderDetail> OrderDetails { get; set; }
        public List<object> Payments { get; set; }
        public OrderDelivery OrderDelivery { get; set; }
    }
  //  http://json2csharp.com/#

}
