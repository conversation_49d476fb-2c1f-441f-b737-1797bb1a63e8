<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    
    <section name="Telerik.Reporting" type="Telerik.Reporting.Configuration.ReportingConfigurationSection, Telerik.Reporting, Version=11.0.17.222, Culture=neutral, PublicKeyToken=a9d7983dfcc261be"/>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
  <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>
  <Telerik.Reporting>
    <Cache provider=""/>
    <cache provider=""/>
    <assemblyReferences>
      <add name="KiotViet.Reports" version="1.0.0.0"/>
    </assemblyReferences>
    <SessionState provider=""/>
    <restReportService>
      <storage provider=""/>
      <reportResolver provider=""/>
    </restReportService>
  </Telerik.Reporting>
  <connectionStrings>
    <add name="KiotViet.Reports.Properties.Settings.KVEntities" connectionString="metadata=res://*/kiotviet.csdl|res://*/kiotviet.ssdl|res://*/kiotviet.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=citirun.com,9047;initial catalog=KiotVietV4;persist security info=True;user id=kiotvietv4;password=**********;MultipleActiveResultSets=True;App=EntityFramework&quot; " providerName="System.Data.SqlClient"/>
    <add name="KiotViet.Reports.Properties.Settings.KiotVietV4" connectionString="Data Source=citirun.com,9047;Initial Catalog=KiotVietV4;Persist Security Info=True;User ID=kiotvietv4;Password=**********;MultipleActiveResultSets=True;Application Name=EntityFramework" providerName="System.Data.EntityClient"/>
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework"/>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
  
  <runtime>
  
       <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
  
            <dependentAssembly>
  
                 <assemblyIdentity name="EntityFramework.MappingAPI" publicKeyToken="7ee2e825d201459e" culture="neutral"/>
  
                 <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
  
            </dependentAssembly>
  
            <dependentAssembly>
  
                 <assemblyIdentity name="Autofac" publicKeyToken="17863af14b0044da" culture="neutral"/>
  
                 <bindingRedirect oldVersion="0.0.0.0-4.6.1.0" newVersion="4.6.1.0"/>
  
            </dependentAssembly>
  
            <dependentAssembly>
  
                 <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
  
                 <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0"/>
  
            </dependentAssembly>
  
            <dependentAssembly>
  
                 <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral"/>
  
                 <bindingRedirect oldVersion="0.0.0.0-2.0.8.0" newVersion="2.0.8.0"/>
  
            </dependentAssembly>
  
            <dependentAssembly>
  
                 <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
  
                 <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
  
            </dependentAssembly>
  
       </assemblyBinding>
  
  </runtime>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2"/></startup></configuration>
