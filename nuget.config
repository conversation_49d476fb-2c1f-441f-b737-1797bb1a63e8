<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" priority="1" />
    <add key="KiotVietFnB" value="http://package.citigo.com.vn/nuget/KiotVietFnB/" priority="2" />
  </packageSources>
  <packageRestore>
    <add key="enabled" value="True" />
    <add key="automatic" value="True" />
  </packageRestore>
  <bindingRedirects>
    <add key="skip" value="False" />
  </bindingRedirects>
  <packageManagement>
    <add key="format" value="1" />
    <add key="disabled" value="False" />
  </packageManagement>
  <packageSourceCredentials>
    <KiotVietFnB>
        <add key="Username" value="fnbdev" />
        <add key="ClearTextPassword" value="d6v2xB7KTZ3G" />
    </KiotVietFnB>
</packageSourceCredentials>
</configuration>
