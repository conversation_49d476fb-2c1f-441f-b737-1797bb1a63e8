﻿using Consul;
using KiotViet.Persistence;
using KiotVietFnB.EntityFramework.Extensions.QueryTagging;
using KvFnBConsul.Configuration;
using KvFnBConsul.Extensions;
using Serilog;
using ServiceStack;
using System;
using System.Configuration;
using EFExtensionsConfig = KiotVietFnB.EntityFramework.Extensions.SqlServer.Configuration;

namespace KiotViet.FnB.Public.Api
{
    public class Global : System.Web.HttpApplication
    {
        private bool IsQueryTaggingApplied => ConfigurationManager.AppSettings["ApplyQueryTagging"]?.ToLower() == "true";
        private readonly object _lockCreateAgent = new object();

        protected void Application_BeginRequest(object src, EventArgs e)
        {
            if (Request.IsLocal)
                ServiceStack.MiniProfiler.Profiler.Start();

            if (Application["FirstRequest"] == null)
            {
                lock (_lockCreateAgent)
                {
                    InitConsulAgent();
                }
            }
        }

        protected void Application_EndRequest(object src, EventArgs e)
        {
            if (Request.IsLocal)
                ServiceStack.MiniProfiler.Profiler.Stop();
        }
        
        protected void Application_Start(object sender, EventArgs e)
        {
            ApplyQueryTagging();
            new AppHost().Init();
        }

        private void ApplyQueryTagging()
        {
            if (IsQueryTaggingApplied)
            {
                //configure extensions for EF
                EFExtensionsConfig.DbContextExtensions.Configure<KVEntities>(config =>
                {

                    config.WithQueryTagging(QueryTagTraceInfo.All);

                });
            }
        }
        protected void Application_End(object sender, EventArgs e)
        {
            try
            {
                var consulConfig = HostContext.TryResolve<IConsulConfig>();
                var consulClient = HostContext.TryResolve<IConsulClient>();
                ConsulServiceRegistryExtensions.StopAgent(consulClient, consulConfig);
            }
            catch (Exception ex)
            {

                Log.Error(ex, "Stop agent failed!");
            }
        }

        private void InitConsulAgent()
        {
            if (Application["FirstRequest"] != null) return;
            Application["FirstRequest"] = true;
            try
            {
                var consulConfig = HostContext.TryResolve<IConsulConfig>();
                var consulClient = HostContext.TryResolve<IConsulClient>();
                ConsulServiceRegistryExtensions.StartAgent(consulClient, consulConfig);
                Log.Warning("Start agent success");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Start agent failed!");
            }
        }
    }
}