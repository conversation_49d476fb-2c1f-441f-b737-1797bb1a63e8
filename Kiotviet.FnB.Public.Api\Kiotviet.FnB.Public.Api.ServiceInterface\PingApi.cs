﻿using ServiceStack;
using ServiceStack.Logging;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    [Route("/ping", Summary = "API Kiểm tra service",
        Notes = "Trả về pong")]
    public class PingRequest : IReturn
    {

    }
    public class PingApi : Service
    {
        private ILog Log = LogManager.GetLogger(typeof(PingApi));
        public string Any(PingRequest req)
        {
          return "PONG";
        }
    }
}