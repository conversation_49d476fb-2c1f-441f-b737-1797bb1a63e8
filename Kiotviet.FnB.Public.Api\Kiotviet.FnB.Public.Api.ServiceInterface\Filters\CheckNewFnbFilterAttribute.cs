﻿using System.Data.SqlClient;
using System.Linq;
using System.Net;
using KiotViet.Persistence;
using KiotViet.Utilities;
using ServiceStack;
using ServiceStack.Caching;
using ServiceStack.Text;
using ServiceStack.Web;

namespace KiotViet.FnB.Public.Api.ServiceInterface.Filters
{
    public class CheckNewFnbFilterAttribute : RequestFilterAttribute
    {
        private readonly string KvRetailerCacheKey = "cache:kvretailerobject:retailer_";

        public override void Execute(IRequest req, IResponse res, object requestDto)
        {
            var retailerCode = Globals.GetRetailerCode();
            var cacheClient = HostContext.TryResolve<ICacheClient>();
            bool? isUsingNewFnb = GetIsUsingNewFnB(retailerCode, cacheClient);

            // Shop new fnb k dùng các API đươc gắn filter này
            if (isUsingNewFnb == true)
            {
                var responseDto = new
                {
                    ResponseStatus = new
                    {
                        ErrorCode = "KvNewFnBException",
                        Message = "Shop New FnB gửi sai api",
                    }
                };
                res.StatusCode = (int)HttpStatusCode.Unauthorized;
                res.ContentType = "application/json";
                res.WriteAsync(responseDto.ToJson());
                res.EndRequest();
            }
        }

        private bool? GetIsUsingNewFnB(string retailerCode, ICacheClient cacheClient)
        {
            var kvRetailer = cacheClient?.Get<KvRetailer>(KvRetailerCacheKey + retailerCode);
            if (kvRetailer != null)
                return kvRetailer?.IsUsingNewFnb;

            var masterData = HostContext.Resolve<KVMasterDataEntities>();
            var isUsingNewFnb = masterData.Database.SqlQuery<bool?>(
               "SELECT TOP 1 IsUsingNewFnb FROM KvRetailer WITH (NOLOCK) WHERE Code = @RetailerCode",
               new SqlParameter("RetailerCode", retailerCode)
           ).FirstOrDefault();

            return isUsingNewFnb;
        }
    }
}