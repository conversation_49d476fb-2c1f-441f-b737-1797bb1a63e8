﻿using KiotViet.Api.Mobile.ServiceModel;
using KiotViet.Mobile.Api;
using KiotViet.Persistence;
using KiotViet.Services.Exception;
using KiotViet.Services.Interface;
using KiotViet.Web.Common.Secure;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using static KiotViet.Api.Mobile.ServiceModel.Sync;

namespace KiotViet.Api.Mobile.ServiceInterface
{
    public class SyncApi : BaseApi
    {
        public IBranchService BranchService { get; set; }
        public ICategoryService CategoryService { get; set; }
        public ICustomerService CustomerService { get; set; }
        public IPriceBookService PriceBookService { get; set; }
        public IPriceBookDetailService PriceBookDetailService { get; set; }
        public ICustomerGroupService CustomerGroupService { get; set; }
        public IBankAccountService BankAccountService { get; set; }
        public IProductService ProductService { get; set; }
        public IProductBranchService ProductBranchService { get; set; }
        public IUserService UserService { get; set; }
        public IPermissionService PermissionService { get; set; }
        public ITimeAccessService TimeAccessService { get; set; }
        public IShadowService ShadowService { get; set; }

        public async Task<SyncDto> Get(SyncGet req)
        {
            DateTime modifiedDate = req.ModifiedDate.Value;
            SyncDto syncReturn = new SyncDto();

            // 1. Branchs
            var branchs = await BranchService.GetAll()
                .Where(b => b.CreatedDate > req.ModifiedDate || (b.ModifiedDate != null && b.ModifiedDate > req.ModifiedDate))
                .ToListAsync();
            syncReturn.Branchs = BranchService.DetachByClone(branchs);
            //syncReturn.BranchRemovedIds = ShadowService.bran

            // 2. Customers
            var customers = await CustomerService.GetAll()
                .Where(c => c.CreatedDate > req.ModifiedDate || (c.ModifiedDate != null && c.ModifiedDate > req.ModifiedDate))
                .ToListAsync();
            syncReturn.Customers = CustomerService.DetachByClone(customers);
            syncReturn.CustomerRemovedIds = await ShadowService.GetDeletedCustomerAsync(CurrentRetailer.Id, modifiedDate);

            // 3. CustomerGroups
            var customerGroups = await CustomerGroupService.GetAll()
                .Where(c => c.CreatedDate > req.ModifiedDate || (c.ModifiedDate != null && c.ModifiedDate > req.ModifiedDate))
                .ToListAsync();
            syncReturn.CustomerGroups = CustomerGroupService.DetachByClone(customerGroups);
            //syncReturn.CustomerGroupRemovedIds = await ShadowService.getde(CurrentRetailer.Id, modifiedDate);

            // 4. PriceBooks
            var priceBooks = await PriceBookService.GetAll()
                .Where(p => p.CreatedDate > req.ModifiedDate || (p.ModifiedDate != null && p.ModifiedDate > req.ModifiedDate))
                .ToListAsync();
            syncReturn.PriceBooks = PriceBookService.DetachByClone(priceBooks);
            //syncReturn.PriceBookRemovedIds = await ShadowService.getdel(CurrentRetailer.Id, modifiedDate);

            // 5. BankAccounts
            var bankAccounts = await BankAccountService.GetAll()
                .Where(b => b.CreatedDate > req.ModifiedDate || (b.ModifiedDate != null && b.ModifiedDate > req.ModifiedDate))
                .ToListAsync();
            syncReturn.BankAccounts = BankAccountService.DetachByClone(bankAccounts);
            syncReturn.BankAccountRemovedIds = await ShadowService.GetDeletedBankAccountAsync(CurrentRetailer.Id, modifiedDate);

            // 6. Categories
            var categories = await CategoryService.GetAll()
                .Where(c => c.CreatedDate > req.ModifiedDate || (c.ModifiedDate != null && c.ModifiedDate > req.ModifiedDate))
                .ToListAsync();
            syncReturn.Categories = CategoryService.DetachByClone(categories);
            syncReturn.CategoryRemovedIds = await ShadowService.GetDeletedCategoryAsync(CurrentRetailer.Id, modifiedDate);

            // 7. Products
            if (req.BranchId.HasValue)
            {
                var products = await ProductBranchService.GetAll()
                    .Where(p => p.CreatedDate > req.ModifiedDate || (p.ModifiedDate != null && p.ModifiedDate > req.ModifiedDate))
                    .ToListAsync();
                syncReturn.Products = ProductBranchService.DetachByClone(products);
                syncReturn.ProductRemovedIds = await ShadowService.GetDeletedProductAsync(CurrentRetailer.Id, modifiedDate);
            }

            // 8. UserPrivileges
            if(req.BranchId.HasValue && req.UserId.HasValue)
            {
                var branchId = req.BranchId.Value;
                var userId = req.UserId.Value;
                var privileges = await UserService.GetPrivilegesByUserAndBranchAsync(userId, branchId);
                var permission = await PermissionService.GetByUserAndBranchAsync(userId, branchId);
                var presentable = privileges.Where(x => x != Payment._Read && x != PurchasePayment._Read).ToDictionary(p => p, p => true);
                var timeaccess = await TimeAccessService.GetByUserIdAsync(userId);
                UserPrivilege userPrivilege = new UserPrivilege()
                {
                    Data = presentable,
                    RoleId = permission?.RoleId,
                    TimeAccess = timeaccess
                };
                syncReturn.UserPrivilege = userPrivilege;
            }

            // 9. PriceBookDetails
            if (req.PriceBookId.HasValue)
            {
                var branchId = req.BranchId.Value;
                var priceBookId = req.PriceBookId.Value;
                var priceBookDetails = await PriceBookDetailService.GetByBranch(branchId).Where(p => p.PriceBookId == priceBookId)
                    .Where(p => p.CreatedDate > req.ModifiedDate || (p.ModifiedDate != null && p.ModifiedDate > req.ModifiedDate))
                    .ToListAsync();
                syncReturn.PriceBookDetails = priceBookDetails;
                syncReturn.PriceBookRemovedIds = await ShadowService.GetDeletedPricebookDetailsAsync(CurrentRetailer.Id, modifiedDate);
            }

            return syncReturn;
        }
    }
}
