﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Web;
using Autofac;
using KiotViet.Exceptions;
using KiotViet.KvSerilog;
using KiotViet.KvSerilog.Utils;
using KiotViet.MongoServices.Impl;
using KiotViet.Persistence;
using KiotViet.Persistence.Common;
using KiotViet.FnB.Public.Api.ServiceInterface;
using KiotViet.RedisServices.Impl;
using KiotViet.Reports.DataSource.Sale;
using KiotViet.Resources;
using KiotViet.Services;
using KiotViet.Services.Common;
using KiotViet.Services.Impl;
using KiotViet.Utilities;
using KiotViet.Web.Common;
using MongoDB.Driver;
using ServiceStack;
using ServiceStack.Auth;
using ServiceStack.Configuration;
using ServiceStack.Logging;
using ServiceStack.Razor;
using ServiceStack.Text;
using KiotViet.Services.Interface;
using KiotViet.ServiceUpdateMultiCache.ServiceInterface;
using KiotViet.ServiceUpdateMultiCache.ServiceInterface.Helper;
using ServiceStack.Caching;
using ServiceStack.Logging.Serilog;
using ServiceStack.Messaging;
using ServiceStack.Web;
using KiotViet.FirebaseRealTimeDatabase.Impl;
using KiotViet.Auth;
using KiotViet.KvSerilog.Enrichers;
using Serilog;
using Serilog.Core;
using Serilog.Formatting.Json;
using KiotVietFnB.BuildInfoTracing.ServiceStackApi;
using KvFnBConsul.Configuration;
using KvFnBConsul.Extensions;
using Funq;
using Consul;
using System.Configuration;
using KiotViet.CommandDispatcher.Abstractions;
using KiotViet.CommandDispatcher;

namespace KiotViet.FnB.Public.Api
{
    public class AppHost : AppHostBase
    {
        /// <summary>
        /// Base constructor requires a Name and Assembly where web service implementation is located
        /// </summary>
        public AppHost()
            : base("KiotViet.FnB.Public.Api", typeof(CategoryApi).Assembly)
        {
            var customSettings = new FileInfo(@"~/appsettings.config".MapHostAbsolutePath());
            AppSettings = customSettings.Exists
                ? (IAppSettings)new TextFileSettings(customSettings.FullName)
                : new AppSettings();

            this.PlugInBuildInfoTracing();
        }
        private ILog Log { get; set; }

        private void WriteToHttpContext(IRequest req)
        {
            try
            {
                if (req == null || !(req.OriginalRequest is HttpRequestWrapper)) return;
                var obj = ContextHelper.GetLogContext(req);
                if (obj == null) return;
                HostContext.RequestContext.Items["KvLogContext"] = obj;
            }
            catch
            {
                //Ignore
            }

        }

        private string GetVersion(Assembly assembly)
        {
            var fvi = FileVersionInfo.GetVersionInfo(assembly.Location);
            return $"{fvi.ProductVersion} - {fvi.Comments}";
        }

        /// <summary>
        /// Configure the given container with the
        /// registrations provided by the funqlet.
        /// </summary>
        /// <param name="container">Container to register.</param>
        public override void Configure(Funq.Container container)
        {

            //Error for handle exception API
            this.ServiceExceptionHandlers.Add((httpReq, request, exception) =>
            {
                if ((exception as HttpError)?.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return DtoUtils.CreateErrorResponse(new object(), exception);
                }
                WriteToHttpContext(httpReq);

                if (!(exception is KvValidateException))
                {
                    Log.Error(exception.Message, exception);
                }
                else
                {
                    Log.Info(exception.Message, exception);
                }

                var innermsg = exception.InnerException?.Message;
                var retailercode = Globals.GetRetailerCode();
                // Handle HTTP errors

                //Check Notexists
                var ex = exception;
                if (!string.IsNullOrEmpty(innermsg))
                {
                    if (innermsg.Contains(string.Format(KVMessage.retailer_DoesNotExist, retailercode)))

                        ex = new KvRetailerNotExistsException(string.Format(KVMessage.retailer_DoesNotExist, retailercode));

                    if (innermsg.Contains(KVMessage.LoginRetailerExpire))
                        ex = new KvRetailerExpireException(KVMessage.LoginRetailerExpire);
                }
                //Check retailer expire

                //or return your own custom response
                return DtoUtils.CreateErrorResponse(new object(), ex);
            });
            //
            this.UncaughtExceptionHandlersAsync.Add(async (req, res, operationName, ex) =>
            {
                if ((ex as HttpError)?.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    return;
                }
                WriteToHttpContext(req);
                if (!(ex is KvValidateException))
                {
                    var statusCode = (ex as HttpError)?.StatusCode;
                    if (statusCode != null && (int)statusCode == (int)ErrorCode.RateLimited)
                    {
                        Log.Warn(ex.Message, ex);
                    } else
                    {
                        Log.Error(ex.Message, ex);
                    }
                }
                else
                {
                    Log.Info(ex.Message, ex);
                }
                if (!ex.Message.Contains(KVMessage.LoginRetailerExpire)) return;
                await res.WriteAsync(" {\"ResponseStatus\": {\"ErrorCode\": \"KvRetailerExpireException\",\"Message\": \"" + KVMessage.LoginRetailerExpire + "\"}}");
                res.ContentType = "application/json; charset=utf-8";
                res.StatusCode = (int)ErrorCode.RetailerExpired;
                await res.EndRequestAsync(skipHeaders: true);
            });

            GlobalResponseFilters.Add((req, resp, reqDTO) =>
            {

                resp.AddHeader("Kv.App", "PublicApi " + Assembly.GetExecutingAssembly().GetName().Version.ToString());
                resp.AddHeader("Kv.Service", Assembly.GetAssembly(typeof(OrderService)).GetName().Version.ToString());
            });

            this.PreRequestFilters.Add((req, resp) =>
            {
                var partner = req.Headers["Partner"];
                if (!string.IsNullOrEmpty(partner))
                {
                    req.Headers["Kv.Partner"] = partner;
                }
                req.Headers["Kv.PublicApiVersion"] = GetVersion(Assembly.GetExecutingAssembly());
                req.Headers["Kv.ServicesVersion"] = GetVersion(Assembly.GetAssembly(typeof(OrderService)));
            });

            container.Register<ICommandDispatcher>(c =>
            {
                var producerConfig = AppSettings.Get<CommandDispatcher.Kafka.Producer.ProducerConfig>("ordering.command.bus");
                return new KafkaCommandDispatcher(producerConfig);
            }).ReusedWithin(ReuseScope.Container);


            // Register by container service stack
            var redisConfigCache = AppSettings.Get<KvRedisConfig>("redis.cache");
            var rediscache = KvRedisPoolManager.GetClientsManager(redisConfigCache);
            container.Register(rediscache);
            container.Register(c =>
            {
                try
                {
                    using (var redis = rediscache.GetClient())
                    {
                        if (redis.Info != null)
                            return rediscache.GetCacheClient();
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex.Message, ex);
                }
                return new MemoryCacheClient();
            });
            var redisConfig = AppSettings.Get<KvRedisConfig>("redis.messagequeue");
            var redismq = KvRedisPoolManager.GetClientsManager(redisConfig);
            container.Register<IMessageFactory>(c => new RedisMessageFactory(redismq));
            Action<ContainerBuilder> configAction = ((builder) =>
            {
                AppSettings = new AppSettings();
                builder.Register(d => new KVMasterDataEntities()).InstancePerRequest();
                builder.Register(d => new KvIdentityEntities()).InstancePerRequest();
                builder.Register<KVEntities>(d =>
                    {
                        var code = Globals.GetRetailerCode();
                        var kvcache = HostContext.Resolve<IKvClientCache>();
                        //Get from Cache 
                        if (kvcache != null)
                        {
                            var cachekey = $"cache:retailerobject:retailer_{code}";
                            var retailer = kvcache.Get<Retailer>(cachekey);
                            if (retailer != null && (retailer.ExpiryDate == default(DateTime) || retailer.ExpiryDate > DateTime.Now))
                            {
                                var cachekeygroup = $"cache:kvgroups:group_{retailer.GroupId}";
                                var group = kvcache.Get<KvGroup>(cachekeygroup);
                                if (group != null)
                                {
                                    return new KVEntities(group.ConnectionString);
                                }
                            }
                        }
                        //Get from Master DB if get cache false
                        var masterData = HostContext.Resolve<KVMasterDataEntities>();
                        return KVEntities.Create(code, masterData);
                    }
                ).InstancePerRequest();
                //Regis Mongo
                builder.Register(
                        d =>
                        {
                            var client = new MongoClient(AppConfigInfo.MongoDbConnection);
                            return client.GetDatabase(AppConfigInfo.MonDataSet);
                        }
                    )
                    .InstancePerRequest();

                //Register All Services
                var assembly = Assembly.GetAssembly(typeof(OrderService));

                builder.RegisterAssemblyTypes(assembly).Where(x => x.Namespace != null && x.Namespace.EndsWith("Services.Impl")).AsImplementedInterfaces().InstancePerRequest();

                assembly = Assembly.GetAssembly(typeof(OrderRealTimeService));
                builder.RegisterAssemblyTypes(assembly).Where(x => x.Namespace != null && x.Namespace.EndsWith("FirebaseRealTimeDatabase.Impl")).AsImplementedInterfaces().InstancePerRequest();

                assembly = Assembly.GetAssembly(typeof(AuditTrailService));
                builder.RegisterAssemblyTypes(assembly).Where(x => x.Namespace != null && x.Namespace.EndsWith("MongoServices.Impl")).AsImplementedInterfaces().InstancePerRequest();

                assembly = Assembly.GetAssembly(typeof(BranchLimitService));
                builder.RegisterAssemblyTypes(assembly)
                    .Where(x => x.Namespace != null && x.Namespace.EndsWith("RedisServices.Impl"))
                    .AsImplementedInterfaces().InstancePerRequest();
                assembly = Assembly.GetAssembly(typeof(SaleDataSource));
                builder.RegisterAssemblyTypes(assembly).Where(x => x.Namespace != null && x.Namespace.IndexOf(".DataSource", StringComparison.Ordinal) > -1).AsSelf();

                builder.Register(c =>
                {
                    var code = Globals.GetRetailerCode();
                    var authService = HostContext.ResolveService<AuthenticateService>(new HttpContextWrapper(HttpContext.Current));

                    KVSession session;
                    try
                    {
                        session = authService.GetSession().ConvertTo<KVSession>();
                    }
                    catch (Exception)
                    {
                        session = null;
                    }

                    var userAuthService = HostContext.Resolve<IUserAuthService>();
                    var kvretailer = userAuthService.GetRetailer(code);
                    var groupid = kvretailer?.GroupId ?? 1;
                    var retailerid = kvretailer?.Id ?? 0;
                    var group = userAuthService.GetGroup(groupid);

                    if (session?.CurrentUser == null)
                    {
                        return new ExecutionContext
                        {
                            Id = (DateTime.Now.Ticks / 10000).ToString(),
                            IpSource = Utilities.Network.GetClientIp(),
                            ClientInfo = Utilities.Network.GetBrowse(),
                            Host = Utilities.Network.GetHostName(),
                            RetailerCode = code,
                            RetailerId = retailerid,
                            GroupId = groupid,
                            IndustryId = kvretailer?.IndustryId ?? 0,
                            Group = group
                        };
                    }


                    IDictionary<string, ISet<int>> permissions = null;
                    if (session.PermissionMap != null)
                    {
                        permissions = new Dictionary<string, ISet<int>>();
                        foreach (var ses in session.PermissionMap)
                        {
                            var map = new HashSet<int>();
                            foreach (var sub in ses.Value)
                            {
                                map.Add(sub);
                            }
                            permissions.Add(ses.Key, map);

                        }
                    }

                    return new ExecutionContext
                    {
                        Id = $"{(DateTime.Now.Ticks).ToString(CultureInfo.InvariantCulture)}{Math.Round(new Random().NextDouble() * retailerid)}",
                        User = session.CurrentUser,
                        RetailerId = retailerid,
                        RetailerCode = session.CurrentRetailerCode,
                        BranchId = session.CurrentBranchId,
                        Permissions = permissions,
                        IpSource = Utilities.Network.GetClientIp(),
                        ClientInfo = Utilities.Network.GetBrowse(),
                        Host = Utilities.Network.GetHostName(),
                        AuthorizedBranchIds = session.PermittedBranchIds ?? new int[] { },
                        IndustryId = session.CurrentIndustryId,
                        GroupId = groupid,
                        Group = group
                    };
                }).InstancePerRequest();


                builder.RegisterType<PosSetting>().AsSelf().InstancePerRequest();
                builder.RegisterType<KvSystemConfigSetting>().AsSelf().InstancePerRequest();
                builder.RegisterType<MetaProvider>().AsSelf();
                var serializerSettings = new Newtonsoft.Json.JsonSerializerSettings
                {
                    NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore,
                    DefaultValueHandling = Newtonsoft.Json.DefaultValueHandling.Populate,
                    DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Local
                };

                builder.Register(c => Newtonsoft.Json.JsonSerializer.Create(serializerSettings));
            });

            container.Register<IConsulConfig>(c =>
            {
                return ConsulServiceRegistryExtensions.GetConsulConfig();
            }).ReusedWithin(ReuseScope.Container);

            container.Register<IConsulClient>(c =>
            {
                var config = HostContext.TryResolve<IConsulConfig>();
                return ConsulServiceRegistryExtensions.BuildConsulClient(config);
            }).ReusedWithin(ReuseScope.Container);

            var mb = new ContainerBuilder();
            configAction.Invoke(mb);
            Plugins.Add(
                new AuthFeature(
                    () => new KVSession(),
                        new IAuthProvider[] {
                         new KvJwtAuthProviderReader(AppSettings) {
                             ValidateToken = (js,req) => req.GetJwtToken().LastRightPart('.').FromBase64UrlSafe().Length >= 32,
                             PopulateSessionFilter = (session, o, arg3) =>
                             {

                                 var sess = session as KVSession;
                                 if (sess == null) return;

                                 var userAuthService = HostContext.TryResolve<IUserAuthService>();
                                 var retailerCode = o["client_RetailerCode"];
                                 var retailerId = ConvertHelper.ToInt32(o["client_RetailerId"]);
                                 var rcode = Globals.GetRetailerCode();
                                 if (string.IsNullOrEmpty(retailerCode) || !retailerCode.Equals(rcode))
                                 {
                                     throw new KvUnauthorizedException(KVMessage.retailer_Invalid);
                                 }
                                 var retailer = userAuthService.GetRetailer(retailerCode);

                                 sess.CurrentRetailerCode = retailerCode;
                                 sess.CurrentRetailerId = retailerId;
                                 sess.CurrentIndustryId = retailer.IndustryId.GetValueOrDefault();

                                 var uid = ConvertHelper.ToInt64(o["client_UserId"]);
                                 var keyuser = uid.ToString();
                                 var cache = HostContext.TryResolve<IKvClientCache>();
                                 var keyuserinfo = $"cache:users:info_{retailer.Id}_{keyuser}";
                                 var currentUser = cache.Get<SessionUser>(keyuserinfo);
                                 if (currentUser == null)
                                 {
                                     var db = HostContext.TryResolve<KVEntities>();
                                     var usertemp = db.Users.FirstOrDefault(u => u.Id == uid && u.RetailerId == retailerId && u.IsActive);

                                     if (usertemp == null)
                                     {
                                         throw HttpError.Unauthorized(KVMessage.userIsNotActive);
                                     }

                                     currentUser = new SessionUser
                                     {
                                         Id = uid,
                                         UserName = usertemp.UserName,
                                         GivenName = usertemp.GivenName,
                                         RetailerId = usertemp.RetailerId,
                                         Type = usertemp.Type,
                                         IsLimitTime = false,
                                         IsAdmin = true,
                                         IsActive = true,
                                         IsLimitedByTrans = false,
                                         IsShowSumRow = true
                                     };
                                     cache.Set(keyuserinfo, currentUser, TimeSpan.FromHours(24));
                                 }
                                 sess.Roles = new List<string>{KVRole.User};
                                 sess.CurrentUser = currentUser;
                                 sess.PermittedBranchIds = userAuthService.GetPermittedBranchId(uid,
                                         sess.CurrentUser.IsAdmin,
                                         sess.CurrentRetailerId)
                                     .ToArray();
                                 sess.Permissions = new List<string>();

                             }}}));

            Plugins.Add(new RazorFormat());

            var autoFacContainer = mb.Build();
            bool isOptimizeAutofac = AppSettings.Get<bool>("IsOptimizeAutofac");
            var adapter = new AutofacIocAdapter(autoFacContainer, container)
            {   
                ConfigAction = isOptimizeAutofac ? null : configAction
            };
            container.Adapter = adapter;

            //Http status code mapping
            SetConfig(new HostConfig
            {
                MapExceptionToStatusCode =
                {
                    {typeof(KvException), 420 },
                    {typeof(TokenException), 401 },
                    {typeof(KvValidateException), 420 },
                    {typeof(KvValidateCustomerException),420},
                    {typeof(KvValidateOrderException),420},
                    {typeof(KvUnauthorizedException), 403},
                    {typeof(KvRetailerExpireException), 402},
                    {typeof(KvRetailerNotExistsException), 404},
                    {typeof(KvCloseBookException), 420},
                    {typeof(NotSupportedException), 420},
                    {typeof(DbException), 420}


                },
                DefaultContentType = MimeTypes.Json,
                EnableFeatures = Feature.All.Remove(
                    Feature.Metadata | Feature.Soap11 | Feature.Soap12)

            });

            ConfigJsonSerializer();
            ConfigLogger();
        }
        private void ConfigJsonSerializer()
        {
            JsConfig.DateHandler = DateHandler.ISO8601;
            JsConfig.EmitCamelCaseNames = true;
            var assembly = Assembly.GetAssembly(typeof(Order));
            var ls = assembly.GetTypes().Where(t => t.Namespace != null && t.Namespace.EndsWith(".Persistence")).OrderByDescending(c => c.Name);
            foreach (var type in ls)
            {
                var methodInfos = type.GetMethod("OnDeserialize", BindingFlags.Public | BindingFlags.Static);
                if (methodInfos == null) continue;
                var genType = typeof(JsConfig<>).MakeGenericType(type);
                var mem = genType.GetProperty("OnDeserializedFn", BindingFlags.Public | BindingFlags.Static);
                mem?.SetValue(null, methodInfos.Invoke(null, null));
            }
        }

        private void ConfigLogger()
        {
            Serilog.Log.Logger = CreateLogger(AppSettings);
            LogManager.LogFactory = new SerilogFactory();

            Log = LogManager.GetLogger(GetType());
            Log.Info("KiotViet FnB Public API is starting...");
        }

        private Logger CreateLogger(IAppSettings appSettings)
        {
            string logPath = appSettings.GetString("serilog:write-to:RollingFile.pathlog");
            int num = appSettings.Get<int>("serilog:write-to:RollingFile.bufferSize");
            string appName = appSettings.GetString("serilog:appName");
            bool isActive = appSettings.Get<bool>("serilog:IsActive");
            string expressionFilter = appSettings.Get<string>("serilog:ExpressionFilter");
            bool isWriteRequest = appSettings.Get<bool>("serilog:IsIsWriteRequest");
            
            var kvLogConfig = new KvLogConfig()
            {
                IsActive = isActive,
                ExpressionFilter = expressionFilter,
                IsIsWriteRequest = isWriteRequest
            };
            if (string.IsNullOrEmpty(logPath))
                logPath = "~\\Logging\\log-{Date}.json";
            if (!Path.IsPathRooted(logPath))
                logPath = logPath.MapServerPath();
            int bufferSize = num > 0 ? num : 10000;

            return new LoggerConfiguration().ReadFrom.AppSettings()
                .WriteTo.Async(a => a.KvRollingFile(new JsonFormatter(), logPath), bufferSize)
                .Enrich.WithMachineName()
                .Enrich.WithProcessId().Enrich.WithProcessName()
                .Enrich.WithMemoryUsage().Enrich.WithThreadId()
                .Enrich.WithKvHttpContextData(appName, kvLogConfig)
                .CreateLogger();
        }
    }

    public class KafkaConfiguration
    {
        public string BootstrapServers { get; set; }
        public string ClientId { get; set; }
        public string Topic { get; set; }
        public int MessageMaxBytes { get; set; }
        public int MaximumRetryProduce { get; set; }
    }
}