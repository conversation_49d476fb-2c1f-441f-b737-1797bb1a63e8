﻿using System;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Services.Interface;
using Linq2Rest;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class BranchApi : BaseApi
    {
        public  IBranchService BranchService { get; set; }

        public async Task<SyncDataSources<BranchDTO>> Get(BranchList req)
        {
            var result = new SyncDataSources<BranchDTO>();
            var baseBranchList = BranchService.GetAllBranchs();
            if (req.LastModifiedFrom != null)
            {
                baseBranchList =
                    baseBranchList.Where(
                        p =>
                            (p.ModifiedDate == null && p.CreatedDate > req.LastModifiedFrom) ||
                            (p.ModifiedDate != null && p.ModifiedDate > req.LastModifiedFrom));
            }
            var branchList = baseBranchList.Where(p => p.IsActive && (p.LimitAccess == null || p.LimitAccess.Value == false)).Select(p => new BranchDTO()
            {
                Id = p.Id,
                Name = p.Name,
                Code = p.Code,
                Address = p.Address,
                LocationName = p.LocationName,
                WardName = p.WardName,
                ContactNumber = p.ContactNumber,
                RetailerId = p.RetailerId,
                Email = p.Email,
                ModifiedDate = p.ModifiedDate,
                CreatedDate = p.CreatedDate

            });
            if (req.IncludeRemoveIds)
            {
                var removeIdList = await baseBranchList.Where(p => p.LimitAccess != null && p.LimitAccess.Value).Select(t => new {t.Id}).ToListAsync();
                if (removeIdList.Any())
                    result.RemovedIds = removeIdList.Select(i => Convert.ToInt64(i.Id)).ToList();
            }
            result.Total = await branchList.CountAsync();
            if (req.Orderby == null) branchList = branchList.OrderBy(p => p.Name);
            var lst = branchList.Filter(req.GetModelFilter());
            lst = lst.Take(req);
            result.PageSize = GetPageSize(req.PageSize);
            result.Data = (await lst.ToListAsync()).Select(x => x.ConvertTo<BranchDTO>()).ToList();
            return result;
        }
    }
}
