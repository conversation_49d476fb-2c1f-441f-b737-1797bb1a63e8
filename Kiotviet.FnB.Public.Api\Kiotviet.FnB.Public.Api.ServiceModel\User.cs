﻿using System;
using System.Runtime.Serialization;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/users", Summary = "API Trả lại toàn bộ danh sách User của cửa hàng đã được xác nhận", Notes = "API Trả lại toàn bộ danh sách User của cửa hàng đã được xác nhận")]
    public class UserList : PageReq, IReturn<SyncDataSources<UserDTO>>
    {
        [ApiMember(Name = "includeRemoveIds", Description = "C<PERSON> lấy thông tin danh sách Id bị xoá dựa trên lastModifiedFrom", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeRemoveIds")]
        public bool IncludeRemoveIds { get; set; }
    }
    [DataContract]
    public class UserDTO
    {
        [DataMember(Name = "id")]
        public long Id { get; set; }
        [DataMember(Name = "userName")]
        public string UserName { get; set; }
        [DataMember(Name = "givenName")]
        public string GivenName { get; set; }
        [DataMember(Name = "address")]
        public string Address { get; set; }
        [DataMember(Name = "mobilePhone")]
        public string MobilePhone { get; set; }
        [DataMember(Name = "retailerId")]
        public int RetailerId { get; set; }
        [DataMember(Name = "email")]
        public string Email { get; set; }
        [DataMember(Name = "birthDate")]
        public DateTime? BirthDate { get; set; }
        [DataMember(Name = "createdDate")]
        public DateTime CreatedDate { get; set; }
        [DataMember(Name = "description")]
        public string Description { get; set; }
    }
}
