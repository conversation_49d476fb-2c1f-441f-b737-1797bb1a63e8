﻿//using NUnit.Framework;
//using ServiceStack;
//using ServiceStack.Testing;
//using KiotViet.FnB.Public.Api.Mobile.ServiceInterface;

//namespace KiotViet.FnB.Public.Api.Mobile.Tests
//{
//    [TestFixture]
//    public class UnitTests
//    {
//        private readonly ServiceStackHost appHost;

//        public UnitTests()
//        {
//            appHost = new BasicAppHost(typeof(CustomerApi).Assembly)
//            {
//                ConfigureContainer = container =>
//                {
//                    //Add your IoC dependencies here
//                }
//            }
//            .Init();
//        }

//        [TestFixtureTearDown]
//        public void TestFixtureTearDown()
//        {
//            appHost.Dispose();
//        }

//        [Test]
//        public void TestMethod1()
//        {
//            var service = appHost.Container.Resolve<CustomerApi>();

//            //var response = await (CustomerResponse)service.Any(new Customer {Id=1 });

//            //Assert.That(response.Result, Is.EqualTo("Hello, World!"));
//        }
//    }
//}
