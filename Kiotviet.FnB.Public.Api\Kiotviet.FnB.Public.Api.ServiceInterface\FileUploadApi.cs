﻿using KiotViet.AmazonS3;
using KiotViet.Api.Mobile.ServiceModel;
using KiotViet.Mobile.Api;
using KiotViet.Persistence;
using KiotViet.Services.Exception;
using KiotViet.Services.Interface;
using KiotViet.Utilities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace KiotViet.Api.Mobile.ServiceInterface
{
    public class FileUploadApi : BaseApi
    {
        public IProductService ProductService { get; set; }
        public IProductImageService ProductImageService { get; set; }

        public async Task<object> Post(FileUpload req)
        {
            if (base.Request.Files == null || base.Request.Files.Length == 0)
            {
                throw new KVValidateException("Tệp tin rỗng");
            }

            foreach (var file in base.Request.Files)
            {
                if (file != null && file.ContentLength > 0)
                {
                    if (file.ContentLength > AppConfigInfo.AwsMaxPhotoSize)
                    {
                        throw new KVValidateException(string.Format(Resources.Labels.files_MaxSize, (AppConfigInfo.AwsMaxPhotoSize / 1048576) + " MB"));
                    }

                    string contentType;
                    using (var stream = ImageUtils.GetThumbnailImage(file.InputStream, AppConfigInfo.AwsMaxPhotoWidth, AppConfigInfo.AwsMaxPhotoHeight, out contentType))
                    {
                        if (contentType == null)
                        {
                            throw new KVValidateException(Resources.Labels.files_BadRequest);
                        }

                        string key = string.Format("{0}/{1}", CurrentRetailer.Code, Guid.NewGuid().ToString("N").ToLower());

                        if (req.ProductId > 0 && string.IsNullOrWhiteSpace(req.SessionUpload))
                        {
                            var objProduct = ProductService.GetById(req.ProductId);
                            if (objProduct != null)
                            {
                                var s3Service = new AmazonS3Service();
                                if (await s3Service.UploadFileAsync(AppConfigInfo.AwsBucketName, key, stream, contentType))
                                {
                                    await ProductImageService.AddAsync(new ProductImage { ProductId = objProduct.Id, Image = AppConfigInfo.AwsCloudFrontUrl + key });
                                }
                            }
                        }
                        else if (!string.IsNullOrEmpty(req.SessionUpload))
                        {
                            var sUpload = req.SessionUpload.Replace("/", "").Replace(".", "").Replace(":", "");
                            var directoryPath = Path.Combine(HttpContext.Current.Server.MapPath(AppConfigInfo.UploadFolderPath), "Temp");
                            if (!Directory.Exists(directoryPath))
                            {
                                Directory.CreateDirectory(directoryPath);
                            }
                            var path = Path.Combine(directoryPath,
                                $"{sUpload}-{Guid.NewGuid().ToString("N")}{Path.GetExtension(file.FileName)}");
                            File.WriteAllBytes(path, stream.ToArray());
                        }
                        else
                        {
                            throw new KVValidateException("Request không hợp lệ");
                        }
                    }
                }
            }

            return new
            {
                ProductId = req.ProductId,
                SessionUpload = req.SessionUpload,
                Message = Resources.KVMessage.success
            };
        }
    }
}
