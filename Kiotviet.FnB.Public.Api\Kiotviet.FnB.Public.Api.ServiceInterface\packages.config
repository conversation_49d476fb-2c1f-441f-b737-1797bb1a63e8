﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Autofac" version="4.6.1" targetFramework="net462" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net462" />
  <package id="LINQKit" version="1.1.2" targetFramework="net462" />
  <package id="log4net" version="2.0.8" targetFramework="net462" />
  <package id="Microsoft.AspNet.Razor" version="3.2.6" targetFramework="net462" />
  <package id="mongocsharpdriver" version="2.2.4" targetFramework="net462" />
  <package id="MongoDB.Bson" version="2.2.4" targetFramework="net462" />
  <package id="MongoDB.Driver" version="2.2.4" targetFramework="net462" />
  <package id="MongoDB.Driver.Core" version="2.2.4" targetFramework="net462" />
  <package id="MSBuildTasks" version="1.5.0.235" targetFramework="net462" developmentDependency="true" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net462" />
  <package id="ServiceStack" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Api.Swagger" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Client" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Common" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Interfaces" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Logging.Log4Net" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.OrmLite" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Razor" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Redis" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Server" version="5.4.0" targetFramework="net462" />
  <package id="ServiceStack.Text" version="5.4.0" targetFramework="net462" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net462" />
  <package id="System.Memory" version="4.5.1" targetFramework="net462" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net462" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.0" targetFramework="net462" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
</packages>