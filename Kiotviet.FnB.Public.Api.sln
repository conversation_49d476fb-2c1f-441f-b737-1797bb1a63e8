﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.28729.10
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KiotViet.FnB.Public.Api", "Kiotviet.FnB.Public.Api\Kiotviet.FnB.Public.Api\KiotViet.FnB.Public.Api.csproj", "{6F86FC59-9DE7-4BF5-8D11-CB7E99DA29FC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KiotViet.FnB.Public.Api.ServiceModel", "Kiotviet.FnB.Public.Api\Kiotviet.FnB.Public.Api.ServiceModel\KiotViet.FnB.Public.Api.ServiceModel.csproj", "{08BEF328-AB7D-48D3-800D-48ACC75C7914}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KiotViet.FnB.Public.Api.ServiceInterface", "Kiotviet.FnB.Public.Api\Kiotviet.FnB.Public.Api.ServiceInterface\KiotViet.FnB.Public.Api.ServiceInterface.csproj", "{582DFB0F-F84D-4E43-9BCA-2A5A86600B7A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KiotViet.FnB.Public.Api.Tests", "Kiotviet.FnB.Public.Api\Kiotviet.FnB.Public.Api.Tests\KiotViet.FnB.Public.Api.Tests.csproj", "{B1665387-EF91-4CA0-82BB-66E5781ED800}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "KiotViet.FnB.Public.Api.WebhookHelper", "Kiotviet.FnB.Public.Api\KiotViet.FnB.Public.Api.WebhookService\KiotViet.FnB.Public.Api.WebhookHelper.csproj", "{91314BD2-08DD-48E1-8539-79C69A491CAF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestServices", "TestServices\TestServices.csproj", "{F638B68C-492B-47EF-9D13-C55FF368F404}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6F86FC59-9DE7-4BF5-8D11-CB7E99DA29FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F86FC59-9DE7-4BF5-8D11-CB7E99DA29FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F86FC59-9DE7-4BF5-8D11-CB7E99DA29FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F86FC59-9DE7-4BF5-8D11-CB7E99DA29FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{08BEF328-AB7D-48D3-800D-48ACC75C7914}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08BEF328-AB7D-48D3-800D-48ACC75C7914}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08BEF328-AB7D-48D3-800D-48ACC75C7914}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08BEF328-AB7D-48D3-800D-48ACC75C7914}.Release|Any CPU.Build.0 = Release|Any CPU
		{582DFB0F-F84D-4E43-9BCA-2A5A86600B7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{582DFB0F-F84D-4E43-9BCA-2A5A86600B7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{582DFB0F-F84D-4E43-9BCA-2A5A86600B7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{582DFB0F-F84D-4E43-9BCA-2A5A86600B7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{B1665387-EF91-4CA0-82BB-66E5781ED800}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B1665387-EF91-4CA0-82BB-66E5781ED800}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B1665387-EF91-4CA0-82BB-66E5781ED800}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B1665387-EF91-4CA0-82BB-66E5781ED800}.Release|Any CPU.Build.0 = Release|Any CPU
		{B1665387-EF91-4CA0-82BB-66E5781ED800}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{91314BD2-08DD-48E1-8539-79C69A491CAF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{91314BD2-08DD-48E1-8539-79C69A491CAF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{91314BD2-08DD-48E1-8539-79C69A491CAF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{91314BD2-08DD-48E1-8539-79C69A491CAF}.Release|Any CPU.Build.0 = Release|Any CPU
		{F638B68C-492B-47EF-9D13-C55FF368F404}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F638B68C-492B-47EF-9D13-C55FF368F404}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F638B68C-492B-47EF-9D13-C55FF368F404}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F638B68C-492B-47EF-9D13-C55FF368F404}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1A431F3C-FFC1-49AC-87EC-DE6464DE09D8}
	EndGlobalSection
EndGlobal
