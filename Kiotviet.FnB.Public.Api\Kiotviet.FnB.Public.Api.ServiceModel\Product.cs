﻿using KiotViet.Persistence;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/products", "GET", Summary = "API lấy danh sách hàng hóa",
    Notes = "API lấy danh sách hàng hóa")]
    public class GetListProducts : PageReq, IReturn<object>
    {
        [ApiMember(Name = "branchIds", Description = "Id chi nhánh cần xem tồn kho",
        ParameterType = "Query", DataType = "List<int>", IsRequired = false)]
        [DataMember(Name = "branchIds")]
        public List<int> BranchIds { get; set; }
        [ApiMember(Name = "categoryId", Description = "Id nhóm hàng", ParameterType = "Query", DataType = "int?", IsRequired = false)]
        [DataMember(Name = "categoryId")]
        public int? CategoryId { get; set; }
        [ApiMember(Name = "masterUnitId", Description = "Id hàng hoá đơn vị", ParameterType = "Query", DataType = "long?", IsRequired = false)]
        [DataMember(Name = "masterUnitId")]
        public long? MasterUnitId { get; set; }
        [ApiMember(Name = "includeInventory", Description = "Có lấy thông tin tồn kho", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeInventory")]
        public bool IncludeInventory { get; set; }
        [ApiMember(Name = "includePricebook", Description = "Có lấy thông tin bảng giá", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includePricebook")]
        public bool IncludePriceBook { get; set; }
        [ApiMember(Name = "includeRemoveIds",Description = "Có lấy thông tin danh sách Id bị xoá dựa trên lastModifiedFrom", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeRemoveIds")]
        public bool IncludeRemoveIds { get; set; }
    }

    [Route("/products/{id}", "GET", Summary = "API lấy thông tin một hàng hóa",Notes = "API lấy thông tin một hàng hóa theo Id")]
    [Route("/products/code/{code}", "GET", Summary = "API lấy thông tin một hàng hóa", Notes = "API lấy thông tin một hàng hóa theo Code")]
    public class GetProduct : IReturn<ProductDTO>
    {
        [ApiMember(Name = "id", Description = "Id hàng hóa",ParameterType = "Path", DataType = "long", IsRequired = true)]
        [DataMember(Name = "id")]
        public long Id { get; set; }

        [ApiMember(Name = "Code", Description = "Mã hàng hóa", ParameterType = "Path", DataType = "string", IsRequired = true)]
        [DataMember(Name = "code")]
        public string Code { get; set; }

        [ApiMember(Name = "branchIds", Description = "Danh sách Id chi nhánh cần xem tồn kho",ParameterType = "Query", DataType = "List<int>", IsRequired = false)]
        [DataMember(Name = "branchIds")]
        public List<int> BranchIds { get; set; }
    }

    [Route("/products", "POST", Summary = "API thêm mới hàng hóa",
    Notes = "API thêm mới hàng hóa")]
    [DataContract]
    public class CreateProduct : ProductDTO, IReturn<ProductDTO>
    {
        [DataMember(Name = "branchId")]
        [ApiMember(Name = "branchId", Description = "Id chi nhánh hiện tại",
        ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int BranchId { get; set; }
    }

    [Route("/products/{id}", Summary = "API cập nhật hàng hóa", Notes = "API cập nhật hàng hóa")]
    [DataContract]
    public class UpdateProduct : ProductDTO, IReturn<ProductDTO>
    {
        [DataMember(Name = "branchId")]
        [ApiMember(Name = "branchId", Description = "Id chi nhánh hiện tại", ParameterType = "Query", DataType = "int", IsRequired = true)]
        public int BranchId { get; set; }
    }

    [Route("/products/{id}", "DELETE", Summary = "API xóa hàng hóa",
    Notes = "API xóa hàng hóa")]
    public class DeleteProduct : IReturn<object>
    {
        [ApiMember(Name = "id", Description = "Id hàng hóa",
        ParameterType = "Path", DataType = "long", IsRequired = true)]
        [DataMember(Name = "id")]
        public long Id { get; set; }
    }
    [DataContract]
    public class ProductResponseDTO : ProductDTO
    {
        [DataMember(Name = "createdDate")]
        public DateTime CreatedDate { get; set; }

        [DataMember(Name = "toppings")]
        [ApiMember(Name = "toppings", Description = "Danh sách món thêm", ParameterType = "Query", IsRequired = false)]
        public IEnumerable<ProductDTO> Toppings { get; set; }

        [DataMember(Name = "formulas")]
        [ApiMember(Name = "formulas", Description = "Danh sách thành phần", ParameterType = "Query", IsRequired = false)]
        public IEnumerable<ProductFormulaDTO> Formulas { get; set; }

        [DataMember(Name = "orderTemplate")]
        [ApiMember(Name = "orderTemplate", Description = "Ghi chú đặt hàng", ParameterType = "Query", DataType = "string", IsRequired = false)]
        public string OrderTemplate { get; set; }

        [DataMember(Name = "productType")]
        [ApiMember(Name = "productType", Description = "Loại hàng hóa", ParameterType = "Query", DataType = "byte?", IsRequired = false)]
        public byte? ProductType { get; set; }

        [DataMember(Name = "isTopping")]
        [ApiMember(Name = "isTopping", Description = "Có là Topping?", ParameterType = "Query", DataType = "bool?", IsRequired = false)]
        public bool? IsTopping { get; set; }

        [DataMember(Name = "isProcessedGoods")]
        [ApiMember(Name = "isProcessedGoods", Description = "Có là hàng chế biến?", ParameterType = "Query", DataType = "bool?", IsRequired = false)]
        public bool? IsProcessedGoods { get; set; }

        [DataMember(Name = "isTimeType")]
        [ApiMember(Name = "isTimeType", Description = "Có là hàng tính giờ?", ParameterType = "Query", DataType = "bool?", IsRequired = false)]
        public bool? IsTimeType { get; set; }

        [DataMember(Name = "isRewardPoint")]
        [ApiMember(Name = "isRewardPoint", Description = "Có là hàng tích điểm?", ParameterType = "Query", DataType = "bool?", IsRequired = false)]
        public bool? IsRewardPoint { get; set; }

        [DataMember(Name = "isTimeServices")]
        [ApiMember(Name = "isTimeServices", Description = "Số lượng tính theo thời gian sử dụng?", ParameterType = "Query", DataType = "bool?", IsRequired = false)]
        public bool? IsTimeServices { get; set; }
    }

    [DataContract]
    public class ProductFormulaDTO : ProductDTO
    {
        [DataMember(Name = "quantity")]
        [ApiMember(Name = "quantity", Description = "Số lượng thành phần", ParameterType = "Query", DataType = "double", IsRequired = false)]
        public double? Quantity { get; set; }
    }

    public class ProductAttributeDto
    {
        public long AttributeId { get; set; }
        public long ProductId { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }
        public long Id { get; set; }
    }
}