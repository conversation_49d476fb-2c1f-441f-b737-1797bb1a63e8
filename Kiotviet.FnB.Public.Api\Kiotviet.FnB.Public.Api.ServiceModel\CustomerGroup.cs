﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using KiotViet.Persistence;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/customers/group", "GET")]
    public class CustomerGroupList : PageReq, IReturn<PagingDataSources<CustomerGroupDto>>
    {
       
    }
    public class CustomerGroupDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int RetailerId { get; set; }
        public double? DiscountRatio { get; set; }
        public decimal? Discount { get; set; }
        public long CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public long? ModifiedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
       
       
      
      
      
        public virtual ICollection<CustomerGroupDetail> CustomerGroupDetails { get; set; }

    }
}
