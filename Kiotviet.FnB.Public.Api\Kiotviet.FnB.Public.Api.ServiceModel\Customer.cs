﻿using System;
using System.Runtime.Serialization;
using ServiceStack;
using System.Collections.Generic;
using KiotViet.Persistence;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/customers", "GET", Summary = "API lấy danh sách khách hàng", Notes = "API lấy danh sách khách hàng")]
    public class CustomerList : PageReq, IReturn<object>
    {
        [ApiMember(Name = "code", Description = "Tìm kiếm khách hàng theo mã khách hàng", ParameterType = "Query", DataType = "string",IsRequired = false)]
        [DataMember(Name = "code")]
        public string Code { get; set; }

        [ApiMember(Name = "name", Description = "Tìm kiếm khách hàng theo tên khách hàng", ParameterType = "Query", DataType = "string", IsRequired = false)]
        [DataMember(Name = "name")]
        public string Name { get; set; }

        [ApiMember(Name = "contactNumber", Description = "Tì<PERSON> kiếm khách hàng theo số điện thoại", ParameterType = "Query", DataType = "string", IsRequired = false)]
        [DataMember(Name = "contactNumber")]
        public string ContactNumber { get; set; }

        [ApiMember(Name = "branchIds", Description = "Id chi nhánh",
        ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "branchIds")]
        public List<int> BranchIds { get; set; }

        [ApiMember(Name = "includeRemoveIds", Description = "Có lấy thông tin danh sách Id bị xoá dựa trên lastModifiedFrom", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeRemoveIds")]
        public bool IncludeRemoveIds { get; set; }

        [ApiMember(Name = "includeTotal", Description = "Có lấy thông tin TotalInvoice, TotalPoint, TotalRevenue", ParameterType = "Query", DataType = "bool", IsRequired = true)]
        [DataMember(Name = "includeTotal")]
        public bool IncludeTotal { get; set; }
        [ApiMember(Name = "includeCustomerGroup", Description = "Có lấy thông tin Groups", ParameterType = "Query", DataType = "bool", IsRequired = true)]
        [DataMember(Name = "includeCustomerGroup")]
        public bool IncludeCustomerGroup { get; set; }
        [ApiMember(Name = "GroupId", Description = @"Id nhóm khách hàng + Id = 0: lấy dữ liệu theo tất cả các nhóm khách hàng",ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "GroupId")]
        public int GroupId { get; set; }
        [ApiMember(Name = "GroupIds", Description = @"Danh sách Id nhóm khách hàng", ParameterType = "Query", DataType = "int[]", IsRequired = false)]
        [DataMember(Name = "GroupIds")]
        public int[] GroupIds { get; set; }
    }

    [Route("/customers/{Id}", "GET", Summary = "API lấy thông tin chi tiết khách hàng", Notes = "API lấy thông tin chi tiết khách hàng theo Id")]
    [Route("/customers/code/{code}", "GET", Summary = "API lấy thông tin chi tiết khách hàng", Notes = "API lấy thông tin chi tiết khách hàng theo Code")]
    public class CustomerDetail : IReturn<CustomerDTO>
    {
        [ApiMember(Name = "Id", Description = "Id khách hàng cần lấy thông tin chi tiết", ParameterType = "Path", DataType = "long", IsRequired = true)]
        public long Id { get; set; }
        [ApiMember(Name = "Code", Description = "Mã khách hàng cần lấy thông tin chi tiết", ParameterType = "Path", DataType = "string", IsRequired = true)]
        public string Code { get; set; }
    }

    [Route("/customers/{Id}", "DELETE", Summary = "API xóa khách hàng", Notes = "API xóa khách hàng")]
    public class CustomerDelete : IReturn<object>
    {
        [ApiMember(Name = "Id", Description = "Id khách hàng cần xóa", ParameterType = "Path", DataType = "long", IsRequired = true)]
        public long Id { get; set; }
    }

    [Route("/customers", "POST", Summary = "API thêm mới khách hàng", Notes = "API thêm mới khách hàng")]
    public class CustomerCreate : CustomerDTO,IReturn<object>
    {
      
    }
    [Route("/customers/{Id}", "PUT", Summary = "API cập nhật khách hàng", Notes = "API cập nhật khách hàng")]
    public class CustomerUpdate : CustomerDTO, IReturn<object>
    {

    }
    [DataContract]
    public class CustomerDTO
    {
        [DataMember(Name = "id")]
        public long Id { get; set; }
        [DataMember(Name = "code")]
        public string Code { get; set; }
        [DataMember(Name = "name")]
        public string Name { get; set; }
        [DataMember(Name = "gender")]
        public bool? Gender { get; set; }
        [DataMember(Name = "birthDate")]
        public DateTime? BirthDate { get; set; }
        [DataMember(Name = "contactNumber")]
        public string ContactNumber { get; set; }
        [DataMember(Name = "address")]
        public string Address { get; set; }
        [DataMember(Name = "retailerId")]
        public int RetailerId { get; set; }
        [DataMember(Name = "branchId")]
        public int? BranchId { get; set; }
        [DataMember(Name = "locationName")]
        public string LocationName { get; set; }
        [DataMember(Name = "email")]
        public string Email { get; set; }
        [DataMember(Name = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }
        [DataMember(Name = "createdDate")]
        public DateTime CreatedDate { get; set; }
        [DataMember(Name = "type")]
        public byte? Type { get; set; }
        [DataMember(Name = "organization")]
        public string Organization { get; set; }
        [DataMember(Name = "taxCode")]
        public string TaxCode { get; set; }
        [DataMember(Name = "comments")]
        public string Comments { get; set; }
        [DataMember(Name = "groups")]
        public string Groups { get; set; }
        [DataMember(Name = "debt")]
        public decimal? Debt { get; set; }
        [DataMember(Name = "totalInvoiced")]
        public decimal? TotalInvoiced { get; set; }
        [DataMember(Name = "totalRevenue")]
        public decimal? TotalRevenue { get; set; }
        [DataMember(Name = "totalPoint")]
        public double? TotalPoint { get; set; }
        [DataMember(Name = "GroupIds")]
        public int[] GroupIds { get; set; }
        [DataMember(Name = "rewardPoint")]
        public long? RewardPoint { get; set; }

    }
}
