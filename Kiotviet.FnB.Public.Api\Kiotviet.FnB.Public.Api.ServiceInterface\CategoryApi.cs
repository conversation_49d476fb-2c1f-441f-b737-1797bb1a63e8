﻿using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using KiotViet.MongoDb.Entity;
using KiotViet.MongoServices.Interface;
using KiotViet.Persistence;
using KiotViet.Persistence.Common;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Resources;
using KiotViet.Exceptions;
using KiotViet.Services.Interface;
using Linq2Rest;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class CategoryApi : BaseApi
    {
        public ICategoryService CategoryService { get; set; }
        public IAuditTrailService AuditTrailService { get; set; }
        public IShadowService ShadowService { get; set; }
        public IBranchService BranchService { get; set; }
        public async Task<SyncDataSources<CategoriesDTO>> Get(CategoryList req)
        {
            var ds = new SyncDataSources<CategoriesDTO>();
            var ls = CategoryService.GetAll().Where(c => !(c.isDeleted ?? false));
            ls = req.HierachicalData
                ? ls
                : (req.LastModifiedFrom != null
                    ? ls.Where(p => (p.ModifiedDate == null && p.CreatedDate >= req.LastModifiedFrom) ||
                                    (p.ModifiedDate != null && p.ModifiedDate >= req.LastModifiedFrom))
                    : ls);
            var result = req.HierachicalData
                ? await GetCategoryWithHierachial(req, ls)
                : await GetCategoryWithModifiedDate(req, ls);

            if (req.IncludeRemoveIds) ds.RemovedIds = await ShadowService.GetDeletedCategoryAsync(CurrentRetailerId, req.LastModifiedFrom);
            ds.PageSize = GetPageSize(req.PageSize);
            ds.Total = await ls.CountAsync();
            ds.Data = result;
            return ds;
        }

        private async Task<List<CategoriesDTO>> GetCategoryWithModifiedDate(CategoryList req, IQueryable<Category> ls)
        {
            var cat = ls.Select(p => new CategoriesDTO
            {
                Id = p.Id,
                ParentId = p.ParentId,
                Name = p.Name,
                RetailerId = p.RetailerId,
                ModifiedDate = p.ModifiedDate,
                CreatedDate = p.CreatedDate
            });
            if (req.Orderby == null) cat = cat.OrderBy(p => p.CreatedDate);
            var lst = cat.Filter(req.GetModelFilter());
            lst = lst.Take(req);
            return (await lst.ToListAsync()).Select(p => p.ConvertTo<CategoriesDTO>()).ToList();
        }

        private async Task<List<CategoriesDTO>> GetCategoryWithHierachial(CategoryList req, IQueryable<Category> ls)
        {
            var lstParent = ls.Where(p => p.ParentId == null)
                .Select(x => new CategoriesDTO
                {
                    Id = x.Id,
                    Name = x.Name,
                    RetailerId = x.RetailerId,
                    ModifiedDate = x.ModifiedDate,
                    CreatedDate = x.CreatedDate
                });
            if (req.Orderby == null) lstParent = lstParent.OrderBy(p => p.CreatedDate);
            var parents = lstParent.Filter(req.GetModelFilter());
            var lst = (await parents.Take(req).ToListAsync()).Select(x => x.ConvertTo<CategoriesDTO>()).ToList();
            var lstParentIds = lst.Select(p => p.Id);
            var lstTwoChild = await ls.Where(p => p.ParentId != null && lstParentIds.Contains(p.ParentId.Value))
                .Select(x => new CategoriesDTO
                {
                    Id = x.Id,
                    ParentId = x.ParentId,
                    Name = x.Name,
                    RetailerId = x.RetailerId,
                    ModifiedDate = x.ModifiedDate,
                    CreatedDate = x.CreatedDate
                }).ToListAsync();
            var lstTwoIds = lstTwoChild.Select(p => p.Id);
            var lstThreeChild = await ls.Where(p => p.ParentId != null && lstTwoIds.Contains(p.ParentId.Value))
                .GroupBy(t => t.ParentId)
                .ToDictionaryAsync(g => g.Key, g => g.Select(x => new CategoriesDTO
                {
                    Id = x.Id,
                    ParentId = x.ParentId,
                    Name = x.Name,
                    HasChild = x.HasChild,
                    RetailerId = x.RetailerId,
                    ModifiedDate = x.ModifiedDate,
                    CreatedDate = x.CreatedDate
                }));
            lstTwoChild.ForEach(x =>
            {
                x.Children = lstThreeChild.ContainsKey(x.Id) ? lstThreeChild[x.Id] : null;
                x.HasChild = x.Children != null && x.Children.Any();
            });
            var lstTwoDic = lstTwoChild.GroupBy(p => p.ParentId)
                .ToDictionary(g => g.Key, g => g.Select(x => x.ConvertTo<CategoriesDTO>()));
            lst.ForEach(x =>
            {
                x.Children = lstTwoDic.ContainsKey(x.Id) ? lstTwoDic[x.Id] : null;
                x.HasChild = x.Children != null && x.Children.Any();
            });
            return lst;
        }

        public async Task<object> Delete(CategoryDelete req)
        {
            var obj = (await CategoryService.GetByIdAsync(req.Id)).ConvertTo<Category>();
            await CategoryService.DeleteAsync(req.Id);
            var catName = obj.Name;

            #region Logs

            try
            {
                if (obj.ParentId != null && obj.ParentId > 0 && obj.Parent != null)
                {
                    if (!string.IsNullOrEmpty(obj.Parent.Name)) catName = obj.Parent.Name + ">>" + catName;
                    if (obj.Parent.ParentId != null && obj.Parent.ParentId > 0 && obj.Parent.Parent != null)
                    {
                        if (!string.IsNullOrEmpty(obj.Parent.Parent.Name)) catName = obj.Parent.Parent.Name + ">>" + catName;
                    }
                }
                var logs = new AuditTrailLog
                {
                    Content = "[PublicAPI] Xóa nhóm hàng: " + catName,
                    FunctionId = (int)FunctionType.Product,
                    Action = (int)AuditTrailAction.Delete,
                    //BranchId = (await BranchService.GetDefaultBranchAsync()).Id
                };
                await AuditTrailService.AddLog(logs);
            }
            catch (KvException ex)
            {
                Log.Error(ex.Message, ex);
            }

            #endregion

            //return new {Message = string.Format(KVMessage.category_DeleteSuccess, catName)};
            return new { Message = "category_DeleteSuccess" };
        }

        public async Task<CategoriesDTO> Get(CategoryDetail req)
        {
            var existing = await CategoryService.GetByIdAsync(req.Id);
            if (existing == null || (existing.isDeleted ?? false)) throw new KvValidateException(KVMessage.category_NotFound);
            var obj = existing.ConvertTo<CategoriesDTO>();
            obj.ModifiedDate = existing.ModifiedDate;
            obj.CreatedDate = existing.CreatedDate;
            var baseChild = CategoryService.GetAll().Select(x => new CategoriesDTO
            {
                Id = x.Id,
                Name = x.Name,
                ParentId = x.ParentId,
                RetailerId = x.RetailerId,
                ModifiedDate = x.ModifiedDate,
                CreatedDate = x.CreatedDate
            });
            var lstTwoChild = baseChild.Where(p => p.ParentId != null && p.ParentId == req.Id).ToList();
            var twoChildIds = lstTwoChild.Select(p => p.Id);
            var lstThreeChild = baseChild.Where(p => p.ParentId != null && twoChildIds.Contains(p.ParentId.Value))
           .GroupBy(t => t.ParentId)
           .ToDictionary(g => g.Key, g => g.Select(x => new CategoriesDTO
           {
               Id = x.Id,
               ParentId = x.ParentId,
               Name = x.Name,
               HasChild = x.HasChild ?? false,
               RetailerId = x.RetailerId,
               ModifiedDate = x.ModifiedDate,
               CreatedDate = x.CreatedDate,
           }));
            lstTwoChild.ForEach(x =>
            {
                x.Children = lstThreeChild.ContainsKey(x.Id) ? lstThreeChild[x.Id] : null;
                x.HasChild = x.Children != null && x.Children.Any();
            });
            obj.Children = obj.HasChild != true ? null : lstTwoChild;
            return obj;
        }

        public async Task<object> Post(CategoryCreate req)
        {
            if (req == null) throw new KvException("Empty category data.");
            if (string.IsNullOrEmpty(req.Name))
                throw new KvValidateException(KVMessage._GlobalValidateSummary);
            var category = new Category()
            {
                Name = req.Name,
                ParentId = req.ParentId
            };
            var obj = await CategoryService.CreateOrUpdateAsync(category);
            var ret = obj.ConvertTo<CategoriesDTO>();
            #region Logs

            var logs = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                Action = (int)AuditTrailAction.Create,
                //BranchId = (await BranchService.GetDefaultBranchAsync()).Id
            };
            var content = obj.Name;
            if (obj.ParentId != null && obj.ParentId > 0 && obj.Parent != null)
            {
                if (!string.IsNullOrEmpty(obj.Parent.Name)) content = obj.Parent.Name + ">>" + content;
                if (obj.Parent.ParentId != null && obj.Parent.ParentId > 0 && obj.Parent.Parent != null &&
                    !string.IsNullOrEmpty(obj.Parent.Parent.Name))
                    content = obj.Parent.Parent.Name + ">>" + content;
            }
            logs.Content = $"[PublicAPI] Tạo mới nhóm hàng: {content}";

            await AuditTrailService.AddLog(logs);
            #endregion
            return new { Message = KVMessage._GlobalSaveSuccess, Data = ret };
        }
        public async Task<object> Put(CategoryUpdate req)
        {
            if (req == null) throw new KvException("Empty category data.");
            if (string.IsNullOrEmpty(req.Name))
                throw new KvValidateException(KVMessage._GlobalValidateSummary);
            var baseCategory = await CategoryService.GetByIdAsync(req.Id);
            var category = baseCategory.ConvertTo<Category>();
            if (!string.IsNullOrEmpty(req.Name)) category.Name = req.Name;
            if (req.ParentId != null) category.ParentId = req.ParentId;
            var obj = await CategoryService.CreateOrUpdateAsync(category);
            var ret = obj.ConvertTo<CategoriesDTO>();
            ret.ModifiedDate = obj.ModifiedDate ?? obj.CreatedDate;
            #region Logs
            var currItem = await CategoryService.GetAll().FirstOrDefaultAsync(c => c.Id == req.Id);
            var nameCurrent = string.Empty;
            if (currItem != null)
            {

                nameCurrent = currItem.Name;
                if (currItem.ParentId != null && currItem.ParentId > 0 && currItem.Parent != null)
                {
                    if (!string.IsNullOrEmpty(currItem.Parent.Name)) nameCurrent = currItem.Parent.Name + ">>" + nameCurrent;
                    if (currItem.Parent.ParentId != null && currItem.Parent.ParentId > 0 &&
                        currItem.Parent.Parent != null && !string.IsNullOrEmpty(currItem.Parent.Parent.Name))
                        nameCurrent = currItem.Parent.Parent.Name + ">>" + nameCurrent;
                }
            }
            var logs = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                Action = (int)AuditTrailAction.Update,
                //BranchId = (await BranchService.GetDefaultBranchAsync()).Id
            };
            var nameUpdated = obj.Name;
            if (obj.ParentId != null && obj.ParentId > 0 && obj.Parent != null)
            {
                if (!string.IsNullOrEmpty(obj.Parent.Name)) nameUpdated = obj.Parent.Name + ">>" + nameUpdated;
                if (obj.Parent.ParentId != null && obj.Parent.ParentId > 0 && obj.Parent.Parent != null &&
                    !string.IsNullOrEmpty(obj.Parent.Parent.Name))
                    nameUpdated = obj.Parent.Parent.Name + ">>" + nameUpdated;
            }
            logs.Content = $"[PublicAPI] Cập nhật nhóm hàng: {nameCurrent} -> {nameUpdated}";
            #endregion
            return new { Message = KVMessage._GlobalSaveSuccess, Data = ret };
        }
    }
}