﻿using System;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using KiotViet.Persistence;
using KiotViet.FnB.Public.Api.ServiceInterface.Filters;
using KiotViet.Services;
using ServiceStack;
using ServiceStack.Logging;
using AppConfigInfo = KiotViet.FnB.Public.Api.ServiceModel.AppConfigInfo;
using KiotViet.Auth;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    //[Mobile.Filters.AccessTimeFilter]
    //[BlackListIpFilter]
    //[RetailerValidFilter]
    //[BranchValidFilter]
    //[RetailerExpireFilter]
    [Authenticate]
    ////[AccessTimeFilterAttribute]
    ////[SessionRestoreFilter]
    //[Mobile.Filters.RetailerExpireFilter(Priority = -1)]
    [RateLimited]
    public class BaseApi : Service
    {
        //private ILog _logger;
        private static readonly ILog Logger = LogManager.GetLogger(typeof(BaseApi));
        public ILog Log => Logger;

        protected int CurrentRetailerId => SessionAs<KVSession>().CurrentRetailerId;
        protected string CurrentRetailerCode => SessionAs<KVSession>().CurrentRetailerCode;
        protected int CurrentIndustryId => SessionAs<KVSession>().CurrentIndustryId;
        protected const string AnotherChannel = "Khác";
        protected string CurrentPartner => !string.IsNullOrEmpty(Request.Headers["partner"]) && Request.Headers["partner"].Equals("Haravan",StringComparison.OrdinalIgnoreCase) ? Request.Headers["partner"] : "Other";

        protected SessionUser CurrentUser
        {
            get { return SessionAs<KVSession>().CurrentUser; }
            set { SessionAs<KVSession>().CurrentUser = value; }
        }

        /// <summary>
        /// Eager load a relation 
        /// </summary>
        /// <typeparam name="TEntity">An EF entity type</typeparam>
        /// <param name="entitySet">IQueryable object </param>
        /// <param name="relations">List of relationship to eager load</param>
        /// <returns>Updated IQueryable set </returns>
        protected virtual IQueryable<TEntity> Include<TEntity>(IQueryable<TEntity> entitySet, string[] relations)
            where TEntity : class
        {
            return relations == null
                ? entitySet
                : relations.Aggregate(entitySet, (current, relation) => current.Include(relation));
        }

        protected string Normallize(double number)
        {
            var result = number.ToString("#,##0.###", new CultureInfo("en-us"));
            return string.IsNullOrEmpty(result) ? "0" : result;
        }

        public string NormallizeWfp(double number)
        {
            var result = number.ToString("#,###", new CultureInfo("en-us"));
            return string.IsNullOrEmpty(result) ? "0" : result;
        }

        protected string DateFormat(DateTime date)
        {
            return date.ToString("dd/MM/yyyy HH:mm:ss");
        }

        protected int GetPageSize(int? pageSize)
        {
            return pageSize == null ? AppConfigInfo.DefaultPageSize : pageSize > AppConfigInfo.MaxPageSize ? AppConfigInfo.MaxPageSize : pageSize.Value;
        }

    }
}