﻿using KiotViet.Persistence;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/invoices/{Id}", "GET", Summary = "API lấy thông tin hóa đơn", Notes = "API lấy thông tin hóa đơn theo Id")]
    [Route("/invoices/code/{code}", "GET", Summary = "API lấy thông tin hóa đơn", Notes = "API lấy thông tin hóa đơn theo Code")]
    public class GetInvoice : IReturn<InvoiceDTO>
    {
        [ApiMember(Name = "Id", Description = "Id hóa đơn", ParameterType = "Path", DataType = "long", IsRequired = true)]
        public long Id { get; set; }
        [ApiMember(Name = "Code", Description = "Mã hóa đơn", ParameterType = "Path", DataType = "string", IsRequired = true)]
        public string Code { get; set; }
    }
    
    [Route("/invoices", "GET", Summary = "API lấy thông tin danh sách hóa đơn",
        Notes = "API lấy thông tin danh sách hóa đơn")]
    public class GetInvoiceList : PageReq, IReturn<object>
    {
        [ApiMember(Name = "status", Description = "Danh sách trạng thái hóa đơn",
            ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "status")]
        public int[] Status { get; set; }

        [ApiMember(Name = "customerIds", Description = "Id khách hàng (Sử dụng trong trường hợp: Lấy danh sách đặt hàng theo Khách hàng, Đối tác)",
            ParameterType = "Query", DataType = "long", IsRequired = false)]
        [DataMember(Name = "customerIds")]
        public long?[] CustomerIds { get; set; }

        [ApiMember(Name = "customerCode", Description = "Mã khách hàng", ParameterType = "Query", DataType = "string", IsRequired = false)]
        [DataMember(Name = "customerCode")]
        public string CustomerCode { get; set; }

        [ApiMember(Name = "branchIds", Description = "Danh sách Id của chi nhánh. Nếu không truyền tham số này thì sẽ mặc định lấy toàn bộ chi nhánh người dùng có quyền",
            ParameterType = "Query", DataType = "String", IsRequired = false)]
        [DataMember(Name = "branchIds")]
        public int[] BranchIds { get; set; }

        [ApiMember(Name = "includePayment", Description = "Có lấy thông tinh danh sách Payments", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includePayment")]
        public bool IncludePayment { get; set; }
        [ApiMember(Name = "includeInvoiceDelivery", Description = "Có lấy thông tin danh sách Invoice Delivery", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "includeInvoiceDelivery")]
        public bool IncludeInvoiceDelivery { get; set; }
        [ApiMember(Name = "toDate", Description = "Thời gian thay đổi dữ liệu lần cuối cho đến ngày ToDate (nhằm hỗ trợ đồng bộ dữ liệu dựa theo TimeStamp)", DataType = "DateTime", IsRequired = false)]
        [DataMember(Name = "toDate")]
        public DateTime? ToDate { get; set; }

        [ApiMember(Name = "orderId", Description = "Id của đơn đặt hàng)", ParameterType = "Query", DataType = "long", IsRequired = false)]
        [DataMember(Name = "orderId")]
        public long? OrderId { get; set; }
        [ApiMember(Name = "createdDate", Description = "Lấy danh sách hóa đơn theo ngày tạo", DataType = "DateTime", IsRequired = false)]
        [DataMember(Name = "createdDate")]
        public DateTime? CreatedDate { get; set; }

        [ApiMember(Name = "fromPurchaseDate", Description = "Từ ngày giao dịch", DataType = "DateTime", IsRequired = false)]
        [DataMember(Name = "fromPurchaseDate")]
        public DateTime? FromPurchaseDate { get; set; }

        [ApiMember(Name = "toPurchaseDate", Description = "Đến ngày giao dịch", DataType = "DateTime", IsRequired = false)]
        [DataMember(Name = "toPurchaseDate")]
        public DateTime? ToPurchaseDate { get; set; }

        [ApiMember(Name = "IncludeSaleChannel", Description = "Có lấy thông tin kênh bán", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "IncludeSaleChannel")]
        public bool IncludeSaleChannel { get; set; }
    }
    [Route("/invoices", "POST", Summary = "API thêm mới hóa đơn", Notes = "API thêm mới hóa đơn")]
    public class CreateInvoice : InvoiceRequest, IReturn<InvoiceDTO>
    {

    }
    [Route("/invoices/{id}", "PUT", Summary = "API cập nhật hóa đơn", Notes = "API cập nhật hóa đơn")]
    public class UpdateInvoice : InvoiceRequest, IReturn<InvoiceDTO>
    {
        [DataMember(Name = "codPaymentMethod")]
        public string CodPaymentMethod { get; set; }
        [DataMember(Name = "codPaymentAccount")]
        public int? CodPaymentAccount { get; set; }
    }
    [DataContract]
    public class InvoiceRequest : InvoiceDTO
    {
        [DataMember(Name = "accountId")]
        public int? AccountId { get; set; }
        [DataMember(Name = "method")]
        public string Method { get; set; }

        public List<SurchagesAddDto> Surchages { get; set; }
    }

    [Route("/invoices", "DELETE", Summary = "API hủy hóa đơn", Notes = "API hủy hóa đơn")]
    public class InvoiceVoid : IReturn<InvoiceDTO>
    {
        [ApiMember(Name = "id", Description = "Id hóa đơn cần hủy", ParameterType = "Query", DataType = "long", IsRequired = true)]
        [DataMember(Name = "id")]
        public long Id { get; set; }
        [ApiMember(Name = "isVoidPayment", Description = "Có hủy luôn phiếu thanh toán liên quan hay không?", ParameterType = "Query", DataType = "bool", IsRequired = false)]
        [DataMember(Name = "isVoidPayment")]
        public bool IsVoidPayment { get; set; }
    }

    [DataContract]
    public class InvoiceDTO
    {
        [DataMember(Name = "id")]
        public long Id { get; set; }
        [DataMember(Name = "uuid")]
        public string Uuid { get; set; }
        [DataMember(Name = "code")]
        public string Code { get; set; }
        [DataMember(Name = "purchaseDate")]
        public DateTime PurchaseDate { get; set; }
        [DataMember(Name = "branchId")]
        public int BranchId { get; set; }
        [DataMember(Name = "branchName")]
        public string BranchName { get; set; }
        [DataMember(Name = "soldById")]
        public long SoldById { get; set; }
        [DataMember(Name = "saleChannelId")]
        public int? SaleChannelId { get; set; }
        [DataMember(Name = "soldByName")]
        public string SoldByName { get; set; }
        [DataMember(Name = "customerId")]
        public long? CustomerId { get; set; }
        [DataMember(Name = "customerCode")]
        public string CustomerCode { get; set; }
        [DataMember(Name = "customerName")]
        public string CustomerName { get; set; }
        [DataMember(Name = "orderId")]
        public long? OrderId { get; set; }
        [DataMember(Name = "orderUuid")]
        public string OrderUuid { get; set; }
        [DataMember(Name = "orderCode")]
        public string OrderCode { get; set; }
        [DataMember(Name = "total")]
        public decimal Total { get; set; }
        [DataMember(Name = "totalPayment")]
        public decimal TotalPayment { get; set; }
        [DataMember(Name = "discount")]
        public decimal? Discount { get; set; }
        [DataMember(Name = "discountRatio")]
        public double? DiscountRatio { get; set; }
        [DataMember(Name = "status")]
        public byte Status { get; set; }
        [DataMember(Name = "statusValue")]
        public string StatusValue { get; set; }
        [DataMember(Name = "description")]
        public string Description { get; set; }
        [DataMember(Name = "usingCod")]
        public bool UsingCod { get; set; }
        [DataMember(Name = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }
        [DataMember(Name = "createdDate")]
        public DateTime CreatedDate { get; set; }
        [DataMember(Name = "invoiceDelivery")]
        public InvoiceDeliveryDTO InvoiceDelivery { get; set; }
        [DataMember(Name = "deliveryDetail")]
        public InvoiceDeliveryDTO DeliveryDetail { get; set; }

        private IEnumerable<InvoiceDetailDTO> _invoiceDetails;
        [DataMember(Name = "invoiceDetails")]
        public IEnumerable<InvoiceDetailDTO> InvoiceDetails
        {
            get => _invoiceDetails;
            set { _invoiceDetails = value?.Where(x => x != null); }
        }

        private IEnumerable<PaymentDTO> _payments;
        [DataMember(Name = "payments")]
        public IEnumerable<PaymentDTO> Payments
        {
            get => _payments;
            set { _payments = value?.Where(x => x != null); }
        }

        [DataMember(Name = "saleChannel")]
        public SaleChannelDto SaleChannel { get; set; }

        private IList<InvoiceOrderSurchargeDto> _invoiceOrderSurcharges;
        [DataMember(Name = "invoiceOrderSurcharges")]
        public IList<InvoiceOrderSurchargeDto> InvoiceOrderSurcharges
        {
            get => _invoiceOrderSurcharges;
            set { _invoiceOrderSurcharges = value?.Where(x => x != null).ToList(); }
        }

        public InvoiceDTO() { }
        public InvoiceDTO(Invoice p)
        {
            Id = p.Id;
            Uuid = p.Uuid;
            OrderId = p.OrderId;
            OrderUuid = p.OrderUuid;
            Code = p.Code;
            PurchaseDate = p.PurchaseDate;
            BranchId = p.BranchId;
            BranchName = p.BranchName;
            SoldById = p.SoldById;
            SoldByName = p.SoldByGivenName;
            SaleChannelId = p.SaleChannelId;
            CustomerId = p.CustomerId;
            CustomerCode = p.CustomerCode;
            CustomerName = p.CustomerName;
            Total = p.Total;
            TotalPayment = p.TotalPayment;
            Discount = p.Discount;
            DiscountRatio = p.DiscountRatio;
            Status = p.Status;
            StatusValue = p.StatusValue;
            Description = p.Description;
            UsingCod = p.UsingCod == 1;
            ModifiedDate = p.ModifiedDate;
            CreatedDate = p.CreatedDate;
            OrderCode = p.Order?.Code;
        }

        public Invoice ConvertTo(InvoiceDTO source)
        {
            var invoice = new Invoice();
            invoice.Id = source.Id;
            invoice.Code = source.Code;
            invoice.BranchId = source.BranchId;
            invoice.SoldById = source.SoldById;
            invoice.CustomerId = source.CustomerId;
            invoice.OrderId = source.OrderId;
            invoice.Discount = source.Discount;
            invoice.DiscountRatio = source.DiscountRatio;
            invoice.UsingCod = (byte)(source.UsingCod == true ? 1 : 0);
            invoice.PurchaseDate = source.PurchaseDate;
            invoice.Description = source.Description;
            invoice.Status = source.Status;
            invoice.InvoiceDetails = source.InvoiceDetails.Select(x => new InvoiceDetail
            {
                ProductId = x.ProductId,
                Quantity = x.Quantity,
                Price = x.Price,
                Discount = x.Discount,
                DiscountRatio = x.DiscountRatio
            }).ToList();
            if (source.UsingCod == true)
            {
                invoice.DeliveryDetail = new InvoiceDelivery()
                {
                    DeliveryBy = source.InvoiceDelivery.PartnerDeliveryId,
                    Receiver = source.InvoiceDelivery.Receiver,
                    Weight = source.InvoiceDelivery.Weight,
                    Length = source.InvoiceDelivery.Length,
                    Width = source.InvoiceDelivery.Width,
                    Height = source.InvoiceDelivery.Height,
                    //Type = source.InvoiceDelivery.Type,
                    Price = source.InvoiceDelivery.Price,
                    Address = source.InvoiceDelivery.Address,
                    ContactNumber = source.InvoiceDelivery.ContactNumber,
                    LocationId = source.InvoiceDelivery.LocationId,
                    DeliveryCode = source.InvoiceDelivery.DeliveryCode
                };
            }
            else
            {
                invoice.DeliveryDetail = null;
            }
            invoice.Payments = source.Payments.Select(x => new Payment
            {
                Id = x.Id,
                Method = x.Method,
                AccountId = x.Method.ToUpper().Trim() == "CASH" ? null : x.AccountId,
                Amount = x.Amount,
                BranchId = invoice.BranchId,
                Description = x.Description
            }).ToList();
            return invoice;
        }
    }
    [DataContract]
    public class InvoiceDetailDTO
    {
        [DataMember(Name = "returnQuantity")]
        public double ReturnQuantity;
        [DataMember(Name = "productId")]
        public long ProductId { get; set; }
        [DataMember(Name = "productCode")]
        public string ProductCode { get; set; }
        [DataMember(Name = "productName")]
        public string ProductName { get; set; }
        [DataMember(Name = "quantity")]
        public double Quantity { get; set; }
        [DataMember(Name = "price")]
        public decimal Price { get; set; }
        [DataMember(Name = "discount")]
        public decimal? Discount { get; set; }
        [DataMember(Name = "discountRatio")]
        public double? DiscountRatio { get; set; }
        [DataMember(Name = "usePoint")]
        public bool? UsePoint { get; set; }
        [DataMember(Name = "subTotal")]
        public decimal SubTotal { get; set; }
        [DataMember(Name = "note")]
        public string Note { get; set; }
        [DataMember(Name = "serialNumbers")]
        public string SerialNumbers { get; set; }
    }

    public class InvoiceOrderSurchargeDto
    {
        public long Id { get; set; }
        public long? InvoiceId { get; set; }
        public long? OrderId { get; set; }
        public int SurchargeId { get; set; }
        public string SurchargeCode { get; set; }
        public string SurchargeName { get; set; }
        public decimal? SurValue { get; set; }
        public double? SurValueRatio { get; set; }
        public decimal Price { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class SurchagesAddDto
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public decimal Price { get; set; }
    }

    public class InvoiceDeliveriesDto
    {
        public long Id { get; set; }
        public long? InvoiceId { get; set; }
        public long? OrderId { get; set; }
        public int? LocationId { get; set; }
        public long? DeliveryBy { get; set; }
        public string Receiver { get; set; }
        public string ContactNumber { get; set; }
        public string Address { get; set; }
        public string LocationName { get; set; }
        public string WardName { get; set; }
        public string DeliveryCode { get; set; }
        public byte? Type { get; set; }
        public decimal? Price { get; set; }
        public byte? UsingPriceCod { get; set; }
        public DateTime? ExpectedDelivery { get; set; }
        public PartnerDeliveryDto PartnerDelivery { get; set; }
    }
}
