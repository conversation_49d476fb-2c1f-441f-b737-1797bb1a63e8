﻿using System;
using ServiceStack;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/webhooks", Summary = "API Trả lại toàn bộ danh sách webhooks của cửa hàng đã đăng ký", 
     Notes = "API Trả lại toàn bộ danh sách webhooks của cửa hàng đã đăng ký")]
    public class WebhookList : PageReq, IReturn<SyncDataSources<WebhookDto>>
    {
    }

    [Route("/webhooks/{Id}", "GET", Summary = "API lấy thông tin webhook", Notes = "API lấy thông tin webhook")]
    public class GetWebhook : IReturn<WebhookDto>
    {
        [ApiMember(Name = "Id", Description = "Id webhook",
        ParameterType = "Path", DataType = "long", IsRequired = true)]
        public long Id { get; set; }
    }

    [Route("/webhooks/{Id}", "DELETE", Summary = "API xóa webhook", Notes = "API xóa webhook")]
    public class RemoveWebhook : IReturn<bool>
    {
        [ApiMember(Name = "Id", Description = "Id webhook", ParameterType = "Path", DataType = "long", IsRequired = true)]
        public long Id { get; set; }
    }

    [Route("/webhooks", "POST", Summary = "API thêm mới hoặc cập nhật webhook", Notes = "API thêm mới hoặc cập nhật webhook")]
    public class CreateOrUpdateWebhook : IReturn<WebhookDto>
    {
        [ApiMember(Name = "Webhook", Description = "Thông tin đơn Webhook", ParameterType = "Query", DataType = "WebhookDto", IsRequired = true)]
        public WebhookDto Webhook { get; set; }
    }

    public class WebhookDto
    {
        public long Id { get; set; }
        public string Type { get; set; }
        public string Url { get; set; }
        public bool IsActive { get; set; }
        public int RetailerId { get; set; }
        public string Description { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }
}
