﻿using System.Linq;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    public static class TakeModelExtension
    {
        public static IQueryable<T> Take<T>(this IQueryable<T> source, PageReq req)
        {
            var pageSize = req.PageSize ?? AppConfigInfo.DefaultPageSize;
            pageSize = pageSize > AppConfigInfo.MaxPageSize ? AppConfigInfo.MaxPageSize : pageSize;
            var skip = req.CurrentItem ?? 0;
            source = source.Skip(skip);
            source = source.Take(pageSize);
            return source;
        }
    }
}
