﻿using System;
using System.Runtime.Caching;
using System.Threading.Tasks;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.FnB.Public.Api.WebhookHelper.Model;
using RestSharp;

namespace KiotViet.FnB.Public.Api.WebhookHelper
{
    public static class RequestHelper<T> where T : class
    {
        public static async Task<T> MakePostRequest(string urlapi, object obj)
        {
            return await MakeRequest(urlapi, obj, Method.POST);
        }
        public static async Task<T> MakeGetRequest(string urlapi)
        {
            return await MakeRequest(urlapi, null, Method.GET);
        }
        public static async Task<T> MakeDeleteRequest(string urlapi, object obj)
        {
            return await MakeRequest(urlapi, obj, Method.DELETE);
        }
        public static async Task<T> MakeRequest(string urlapi, object obj, Method method)
        {
            var accessToken = await GetAccessToken();
            var url = $"{AppConfigInfo.WebhookServer}{urlapi}";
            var client = new RestClient(url);
            var request = new RestRequest(method);
            request.AddHeader("content-type", "application/json");
            request.AddHeader("authorization", $"Bearer {accessToken}");
            if (obj != null)
            {
                request.AddJsonBody(obj);
            }
            var response = await client.ExecuteTaskAsync<T>(request);
            if (response.ErrorException != null)
            {
                throw response.ErrorException;
            }
            return response.Data;
        }
        
        private static async Task<string> GetAccessToken()
        {
            var cache = MemoryCache.Default;
            var accessToken = string.Empty;
            if (cache.Contains(Constants.AccessToken))
            {
                accessToken = cache.Get(Constants.AccessToken).ToString();
            }
            if (string.IsNullOrEmpty(accessToken))
            {
                return await Login();
            }
            return accessToken;
        }

        private static async Task<string> Login()
        {
            try
            {
                var url = $"{AppConfigInfo.WebhookServer}/Token";
                var client = new RestClient(url);
                var request = new RestRequest(Method.POST);
                var username = AppConfigInfo.WebhookUserName;
                var password = AppConfigInfo.WebhookPassword;
                request.AddHeader("content-type", "application/x-www-form-urlencoded");
                request.AddParameter("application/x-www-form-urlencoded", $"grant_type=password&username={username}&password={password}", ParameterType.RequestBody);
                var response = await client.ExecuteTaskAsync<LoginReponse>(request);
                var cache = MemoryCache.Default;
                var expire = TimeSpan.FromMilliseconds(response.Data.ExpiresIn);
                // cache.Contains(Constants.AccessToken)
                cache.Set(Constants.AccessToken, response.Data.AccessToken, DateTimeOffset.Now.Add(expire));
                return response.Data.AccessToken;
            }
            catch (Exception ex)
            {

                Console.WriteLine(ex.Message);
                return string.Empty;
            }

        }

    }
}
