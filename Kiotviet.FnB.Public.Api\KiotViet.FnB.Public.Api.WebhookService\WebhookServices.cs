﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using KiotViet.FnB.Public.Api.WebhookHelper.Model;

namespace KiotViet.FnB.Public.Api.WebhookHelper
{
    public static class WebhookServices
    {

        /// <summary>
        /// Create a Filter
        /// </summary>
        /// <param name="name">Filter Name</param>
        /// <param name="description">Filter Description</param>
        /// <returns></returns>
        public static async Task<bool> CreateFilter(string name, string description)
        {
            var request = new FilterEvent { Name = name, Decription = description };
            var urlapi = "/api/filterapi";
            var retval = await RequestHelper<object>.MakePostRequest(urlapi, request);

            return Convert.ToBoolean(retval);
        }
        /// <summary>
        /// Delete filter by Name
        /// </summary>
        /// <param name="name">Filter Name</param>
        /// <returns></returns>
        public static async Task<bool> DeleteFilter(string name)
        {

            var urlapi = $"/api/filterapi?filter={name}";
            var retval = await RequestHelper<object>.MakeDeleteRequest(urlapi, null);

            return Convert.ToBoolean(retval);
        }

        public static async Task<WebhookReponse> RegisterWebhook(Webhook obj)
        {
            var urlapi = "/api/webhooks/registrations";
            var retval = await RequestHelper<WebhookReponse>.MakePostRequest(urlapi, obj);

            return retval;
        }
        public static async Task<WebhookReponse> GetWebhook(string id)
        {
            var urlapi = $"/api/webhooks/registrations/{id}";
            var retval=   await RequestHelper<WebhookReponse>.MakeGetRequest(urlapi);
            return retval;
        }
        public static async Task<List<WebhookReponse>> GetWebhooks()
        {
            var urlapi = $"/api/webhooks/registrations";
            var retval = await RequestHelper<List<WebhookReponse>>.MakeGetRequest(urlapi);
            return retval;
        }
        public static async Task<bool> UnRegisterWebhook(string id)
        {
            var urlapi = $"/api/webhooks/registrations/{id}";
            await RequestHelper<string>.MakeDeleteRequest(urlapi,null);

            return true;
        }

        public static async Task<int> Notify(string eventkey,int retailerId,object data)
        {
            var evt = $"{eventkey}.{retailerId}";
            var urlapi = "/api/notifyapi";
            var datapost = new {Event = evt, Data = data};
            var result = await RequestHelper<object>.MakePostRequest(urlapi, datapost);
            return Utilities.ConvertHelper.ToInt32(result);

        }
    }
}