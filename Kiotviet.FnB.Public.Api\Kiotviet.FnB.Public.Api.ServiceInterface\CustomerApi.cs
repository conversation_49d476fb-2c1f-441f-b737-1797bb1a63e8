﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using KiotViet.MongoDb.Entity;
using KiotViet.MongoServices.Interface;
using KiotViet.Persistence;
using KiotViet.Persistence.Common;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Resources;
using KiotViet.Exceptions;
using KiotViet.Services.Interface;
using Linq2Rest;
using ServiceStack;
using KiotViet.Services.Common;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class CustomerApi : BaseApi
    {
        public ICustomerService CustomerService { get; set; }
        public IAuditTrailService AuditTrailService { get; set; }
        public IShadowService ShadowService { get; set; }
        public IAuthService AuthService { get; set; }
        public IBranchService BranchService { get; set; }
        public ICustomerGroupService CustomerGroupService { get; set; }
        public PosSetting PosSetting { get; set; }

        public async Task<object> Get(CustomerList req)
        {
            var ds = new SyncDataSources<CustomerDTO>();

            if (req.IncludeTotal)
            {
                var baselst = CustomerService.GetAllWithTotalInvoiced(req.LastModifiedFrom, null, req.GroupId);

                if (!string.IsNullOrEmpty(req.Code))
                {
                    baselst = baselst.Where(c => c.Code.Contains(req.Code.Trim()));
                }
                if (!string.IsNullOrEmpty(req.Name))
                {
                    baselst = baselst.Where(c => c.Name.Contains(req.Name.Trim()));
                }
                if (!string.IsNullOrEmpty(req.ContactNumber))
                {
                    baselst = baselst.Where(c => c.ContactNumber.Contains(req.ContactNumber.Trim()));
                }
               
                if (PosSetting.ManagerCustomerByBranch)
                {
                    var authorizedIds = AuthService.GetAuthorizedBranchesByGroup(Customer._Read);
                    var branchIds = req.BranchIds != null && req.BranchIds.Any()
                        ? authorizedIds.Where(req.BranchIds.Contains).Distinct().ToArray()
                        : authorizedIds;
                    baselst = baselst.Where(c => branchIds.Contains(c.BranchId));
                }
                if (req.LastModifiedFrom != null)
                {
                    baselst =
                        baselst.Where(
                            c =>
                                (c.ModifiedDate == null && c.CreatedDate > req.LastModifiedFrom) ||
                                (c.ModifiedDate != null && c.ModifiedDate > req.LastModifiedFrom));
                }
                var custLst = await baselst.ToListAsync();
                if (req.IncludeCustomerGroup)
                {
                    foreach (var cust in custLst)
                    {
                        var str = string.Empty;
                        var lstGroups = await CustomerGroupService.GetAll()
                            .SelectMany(c => c.CustomerGroupDetails.Where(a => a.CustomerId == cust.Id).Select(b => new { b.CustomerGroup.Name, b.CustomerId })).ToListAsync();
                        var group = lstGroups.Where(g => g.CustomerId == cust.Id).Select(g2 => g2.Name);
                        str = group.Aggregate(str, (current, customerGroup) => current + (customerGroup + "|"));
                        cust.Groups = str.Trim('|');
                    }
                }

                var lst = custLst.Select(c => new CustomerDTO
                {
                    Id = c.Id,
                    Code = c.Code,
                    Name = c.Name,
                    RetailerId = c.RetailerId,
                    BranchId = c.BranchId,
                    Gender = c.Gender,
                    BirthDate = c.BirthDate,
                    CreatedDate = c.CreatedDate,
                    ContactNumber = c.ContactNumber,
                    Address = c.Address,
                    LocationName = c.LocationName,
                    Email = c.Email,
                    ModifiedDate = c.ModifiedDate,
                    Type = c.Type,
                    Organization = c.Organization,
                    TaxCode = c.TaxCode,
                    Comments = c.Comments,
                    Groups = c.Groups,
                    Debt = c.Debt ?? 0,
                    TotalInvoiced = c.TotalInvoiced,
                    TotalPoint = c.TotalPoint,
                    TotalRevenue = c.TotalRevenue,
                    RewardPoint = c.RewardPoint
                });
                ds.Total = custLst.Count();
                if (req.Orderby == null) lst = lst.OrderByDescending(c => c.ModifiedDate);
                var ret = lst.Filter(req.GetModelFilter());
                ret = ret.Take(req);
                ds.Data = ret.Select(x => x.ConvertTo<CustomerDTO>()).ToList();
                ds.PageSize = GetPageSize(req.PageSize);
                if (req.IncludeRemoveIds)
                {
                    var shadowTemps = await ShadowService.GetDeletedCustomerAsync(CurrentRetailerId, req.LastModifiedFrom);
                    var lstSoftIsDeleted = await CustomerService.GetAll().Where(p => (p.isDeleted ?? false) && ((p.ModifiedDate == null && p.CreatedDate >= req.LastModifiedFrom) ||
                                                                                                                (p.ModifiedDate != null && p.ModifiedDate >= req.LastModifiedFrom))).Select(p => p.Id).ToListAsync();
                    if (lstSoftIsDeleted.Any())
                    {
                        shadowTemps = shadowTemps.Union(lstSoftIsDeleted).Distinct().ToList();
                    }
                    ds.RemovedIds = shadowTemps;
                }
                return ds;
            }
            var baseLstCus = CustomerService.GetAll().Where(p => !(p.isDeleted ?? false)).AsNoTracking();
            if (req.LastModifiedFrom != null)
                baseLstCus = baseLstCus.Where(
                    c =>
                        c.CreatedDate > req.LastModifiedFrom ||
                        (c.ModifiedDate != null && c.ModifiedDate > req.LastModifiedFrom));
            if (!string.IsNullOrEmpty(req.Code))
            {
                baseLstCus = baseLstCus.Where(c => c.Code.Contains(req.Code.Trim()));
            }
            if (!string.IsNullOrEmpty(req.Name))
            {
                baseLstCus = baseLstCus.Where(c => c.Name.Contains(req.Name.Trim()));
            }
            if (!string.IsNullOrEmpty(req.ContactNumber))
            {
                baseLstCus = baseLstCus.Where(c => c.ContactNumber.Contains(req.ContactNumber.Trim()));
            }
            
            if (PosSetting.ManagerCustomerByBranch)
            {
                var authorizedIds = AuthService.GetAuthorizedBranchesByGroup(Customer._Read);
                var branchIds = req.BranchIds != null && req.BranchIds.Any()
                    ? authorizedIds.Where(req.BranchIds.Contains).Distinct().ToArray()
                    : authorizedIds;
                baseLstCus = baseLstCus.Where(c => c.BranchId.HasValue && branchIds.Contains(c.BranchId.Value));
            }
            if (req.LastModifiedFrom != null)
            {
                baseLstCus =
                    baseLstCus.Where(
                        c =>
                            (c.ModifiedDate == null && c.CreatedDate > req.LastModifiedFrom) ||
                            (c.ModifiedDate != null && c.ModifiedDate > req.LastModifiedFrom));
            }
            if (req.GroupId > 0)
            {
                baseLstCus = baseLstCus.Where(c => c.CustomerGroupDetails.Any(g => g.GroupId == req.GroupId));
            }

            var custList = await baseLstCus.ToListAsync();
            if (req.IncludeCustomerGroup)
            {
                foreach (var cust in custList)
                {
                    var str = string.Empty;
                    var lstGroups = await CustomerGroupService.GetAll()
                        .SelectMany(c => c.CustomerGroupDetails.Where(a => a.CustomerId == cust.Id).Select(b => new { b.CustomerGroup.Name, b.CustomerId })).ToListAsync();
                    var group = lstGroups.Where(g => g.CustomerId == cust.Id).Select(g2 => g2.Name);
                    str = group.Aggregate(str, (current, customerGroup) => current + (customerGroup + "|"));
                    cust.Groups = str.Trim('|');
                }
            }

            var lstCus = custList.Select(c => new CustomerDTO
            {
                Id = c.Id,
                Code = c.Code,
                Name = c.Name,
                RetailerId = c.RetailerId,
                BranchId = c.BranchId,
                Gender = c.Gender,
                BirthDate = c.BirthDate,
                ContactNumber = c.ContactNumber,
                Address = c.Address,
                LocationName = c.LocationName,
                Email = c.Email,
                CreatedDate = c.CreatedDate,
                ModifiedDate = c.ModifiedDate,
                Type = c.Type,
                Organization = c.Organization,
                TaxCode = c.TaxCode,
                Comments = c.Comments,
                Groups = c.Groups,
                Debt = c.Debt ?? 0,
                RewardPoint = c.RewardPoint
            });
            ds.Total = custList.Count();
            if (req.Orderby == null) lstCus = lstCus.OrderByDescending(c => c.ModifiedDate);
            var cus = lstCus.Filter(req.GetModelFilter());
            cus = cus.Take(req);
            ds.Data = cus.Select(x => x.ConvertTo<CustomerDTO>()).ToList();
            ds.PageSize = GetPageSize(req.PageSize);
            if (req.IncludeRemoveIds)
            {
                var shadowTemps = await ShadowService.GetDeletedCustomerAsync(CurrentRetailerId, req.LastModifiedFrom);
                var lstSoftIsDeleted = await CustomerService.GetAll().Where(p => (p.isDeleted ?? false) && ((p.ModifiedDate == null && p.CreatedDate >= req.LastModifiedFrom) ||
                                                                                                            (p.ModifiedDate != null && p.ModifiedDate >= req.LastModifiedFrom))).Select(p => p.Id).ToListAsync();
                if (lstSoftIsDeleted.Any())
                {
                    shadowTemps = shadowTemps.Union(lstSoftIsDeleted).Distinct().ToList();
                }
                ds.RemovedIds = shadowTemps;
            }
            return ds;
        }

        public async Task<CustomerDTO> Get(CustomerDetail req)
        {
            var cust = req.Id > 0 ? await CustomerService.GetAllWithTotalInvoiced().Where(c => c.Id == req.Id).FirstOrDefaultAsync() : await CustomerService.GetAllWithTotalInvoiced().Where(c => c.Code == req.Code).FirstOrDefaultAsync();
            if (cust == null) throw new KvException(KVMessage.NotFound);
            var str = string.Empty;
            var lstGroups = await CustomerGroupService.GetAll()
                .SelectMany(c => c.CustomerGroupDetails.Where(a => a.CustomerId == cust.Id).Select(b => new { b.CustomerGroup.Name, b.CustomerId })).ToListAsync();
            var group = lstGroups.Where(g => g.CustomerId == cust.Id).Select(g2 => g2.Name);
            str = group.Aggregate(str, (current, customerGroup) => current + (customerGroup + "|"));
            cust.Groups = str.Trim('|');
            var ret = cust.ConvertTo<CustomerDTO>();
            return ret;
        }

        public async Task<object> Delete(CustomerDelete req)
        {
            await CustomerService.DeleteAsync(req.Id);

            #region Logs

            var customer = await CustomerService.GetByIdAsync(req.Id);

            var logs = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Customer,
                Action = (int)AuditTrailAction.Delete,
                RetailerId = CurrentRetailerId,
                BranchId = customer.BranchId != null ? customer.BranchId.Value : (await BranchService.GetDefaultBranchAsync()).Id,
                Content = $"Public API - Xóa khách hàng: {req.Id}"
            };
            await AuditTrailService.AddLog(logs);

            #endregion

            //CustomerService.SendToEsDelete(new CustomersRemoveModel { Ids = new long[] { }, RetailerId = AuthService.Context.RetailerId });

            return new { req.Id, Message = KVMessage._GlobalDeleteSuccess };
        }

        public async Task<object> Post(CustomerCreate req)
        {
            if (req == null) throw new KvException("Empty customer info");
            // validate branch for manage customer by branch
            //if (!req.BranchId.HasValue)
            //{
            //    throw new KvValidateException("Bạn chưa nhập chi nhánh");
            //}
            //var branch = await BranchService.GetByIdAsync(req.BranchId ?? 0);
            //if (branch == null)
            //{
            //    throw new KvValidateException(KVMessage.MsgBranchIdEmpty);
            //}
            //if (branch.LimitAccess == true)
            //{
            //    throw new KvValidateException($"Chi nhánh {branch.Name} đã ngừng hoạt động");
            //}
            req.Id = 0;
            var customerInsert = req.ConvertTo<Customer>();
            if (req.GroupIds != null && req.GroupIds.Any())
            {
                var customerGroups = await CustomerGroupService.GetAll().ToListAsync();
                if (req.GroupIds.Any(x => !customerGroups.Select(c => c.Id).Contains(x)))
                {
                    throw new KvException("Nhóm khách hàng không tồn tại");
                }
                customerInsert.CustomerGroupDetails = req.GroupIds.Select(g => new CustomerGroupDetail
                {
                    GroupId = g
                }).ToList();
            }
            var customer = await InsertCustomer(customerInsert);
            await WriteLog(customer, req);

            //CustomerService.SendToEsAddOrChange(new CustomerAddModel { Ids = new long[] { customer.Id }, Zone = AuthService.Context.Group.Id, RetailerId = AuthService.Context.RetailerId });

            return new { Message = KVMessage._GlobalSaveSuccess, Data = customer };
        }

        public async Task<object> Put(CustomerUpdate req)
        {
            if (req == null) throw new KvException("Empty customer info");
            var cus = req.ConvertTo<Customer>();
            var existingCustomer = CustomerService.DetachByClone(await CustomerService.GetByIdAsync(req.Id), new[] { "CustomerGroupDetails" });
            if (existingCustomer == null) throw new KvValidateException(KVMessage.CustomerNotFound);
            cus = CheckAndUpdateCustomer(existingCustomer, req);
            existingCustomer.Groups = GetGroup(existingCustomer.Id);
            if (req.GroupIds != null)
            {
                var customerGroups = await CustomerGroupService.GetAll().ToListAsync();
                if (req.GroupIds.Any(x => !customerGroups.Select(c => c.Id).Contains(x)))
                {
                    throw new KvException("Nhóm khách hàng không tồn tại");
                }
                cus.CustomerGroupDetails = req.GroupIds.Select(g => new CustomerGroupDetail
                {
                    GroupId = g
                }).ToList();
            }
            else
            {
                cus.CustomerGroupDetails = existingCustomer.CustomerGroupDetails;
            }

            var customer = CustomerService.DetachByClone(await CustomerService.CreateOrUpdateAsync(cus), new[] { "CustomerGroupDetails" });
            var customerDto = customer.ConvertTo<CustomerDTO>();
            customer.Groups = GetGroup(customer.Id);
            customerDto.GroupIds = req.GroupIds;
            await WriteLog(existingCustomer, customerDto);

            //CustomerService.SendToEsAddOrChange(new CustomerAddModel { Ids = new long[] { customer.Id }, Zone = AuthService.Context.Group.Id, RetailerId = AuthService.Context.RetailerId });

            return new { Message = KVMessage._GlobalSaveSuccess, Data = customer.ConvertTo<CustomerDTO>() };
        }

        private async Task<object> WriteLog(Customer existedCustomer, CustomerDTO req)
        {
            var flag = req.Id <= 0;
            var updateName = string.Empty;
            var updateCode = string.Empty;
            var groupName = string.Empty;
            var updateTax = string.Empty;
            var newGroupName = string.Empty;
            newGroupName = GetGroup(req.Id);
            if (flag)
            {
                if (req.GroupIds != null && req.GroupIds.Any())
                {
                    groupName = $", thêm nhóm KH: {GetGroup(existedCustomer.Id)}";
                }
            }
            else
            {
                //var oldCustomer = CustomerService.DetachByClone(await CustomerService.GetByIdAsync(req.Id), new[] { "CustomerGroupDetails" });

                if (existedCustomer != null)
                {
                    updateName = req.Name;
                    updateCode = req.Code;
                    if (req.GroupIds != null)
                    {
                        var oldGroups = existedCustomer.CustomerGroupDetails.Select(x => x.GroupId);
                        var newGroups = req.GroupIds.Select(x => x);
                        var delGroup = oldGroups.Except(newGroups).ToList();
                        var addGroup = newGroups.Except(oldGroups).ToList();
                        if (delGroup.Count > 0 || addGroup.Count > 0)
                        {
                            if (addGroup.Count == 0 && !newGroups.Any())
                            {
                                groupName = $", xóa nhóm KH: {existedCustomer.Groups}";
                            }
                            else if (delGroup.Count == 0 && !oldGroups.Any())
                            {
                                groupName = $", thêm nhóm KH: {newGroupName}";
                            }
                            else
                            {
                                groupName = $", cập nhật nhóm KH: {existedCustomer.Groups}->{newGroupName}";
                            }
                        }
                    }
                    updateTax = req.TaxCode;
                }
            }

            var logs = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Customer,
                Action = flag ? (int)AuditTrailAction.Create : (int)AuditTrailAction.Update,
                RetailerId = CurrentRetailerId,
                BranchId = existedCustomer.BranchId != null ? existedCustomer.BranchId.Value : (await BranchService.GetDefaultBranchAsync()).Id
            };
            if (flag)
            {
                logs.Content =
                    $"Public API - Thêm mới khách hàng mã KH: [CustomerCode]{existedCustomer.Code}[/CustomerCode], tên KH: {existedCustomer.Name}{groupName}";
            }
            else
            {
                updateName = updateName != existedCustomer.Name
                    ? $"{existedCustomer.Name}->{updateName}"
                    : existedCustomer.Name;
                updateCode = updateCode != existedCustomer.Code
                    ? $"{existedCustomer.Code}->[CustomerCode]{updateCode}[/CustomerCode]"
                    : $"[CustomerCode]{existedCustomer.Code}[/CustomerCode]";
                updateTax = updateTax != existedCustomer.TaxCode ? $"{existedCustomer.TaxCode} --> {updateTax}" : $"{existedCustomer.TaxCode}";
                logs.Content = $"Public API - Cập nhật thông tin khách hàng mã KH: {updateCode}, tên KH: {updateName}{groupName}, mã số thuế : {updateTax}";
            }
            await AuditTrailService.AddLog(logs);
            return true;
        }

        public string GetGroup(long customerId)
        {
            var str = string.Empty;
            var group =
                CustomerGroupService.GetAll()
                    .SelectMany(
                        c => c.CustomerGroupDetails.Where(a => a.CustomerId == customerId).Select(b => b.CustomerGroup.Name))
                    .ToList();
            str = group.Aggregate(str, (current, customerGroup) => current + (customerGroup + "|"));
            return str.Trim('|');
        }

        private async Task<Customer> InsertCustomer(Customer customerInsert)
        {
            customerInsert.TotalInvoiced = 0;
            customerInsert.TotalPoint = 0;
            customerInsert.Type = 0;
            customerInsert.ModifiedDate = null;
            customerInsert.Debt = 0;
            customerInsert.RewardPoint = 0;
            return CustomerService.DetachByClone(await CustomerService.CreateOrUpdateAsync(customerInsert), new[] { "CustomerGroupDetails" });
        }

        private Customer CheckAndUpdateCustomer(Customer oldCustomer, CustomerDTO newCustomer)
        {
            var copyObject = new Customer();
            copyObject.CopyFrom(oldCustomer);
            copyObject.Code = !string.IsNullOrEmpty(newCustomer.Code) ? newCustomer.Code : oldCustomer.Code;
            copyObject.Name = !string.IsNullOrEmpty(newCustomer.Name) ? newCustomer.Name : oldCustomer.Name;
            copyObject.Gender = newCustomer.Gender ?? oldCustomer.Gender;
            copyObject.BirthDate = newCustomer.BirthDate ?? oldCustomer.BirthDate;
            copyObject.ContactNumber = !string.IsNullOrEmpty(newCustomer.ContactNumber) ? newCustomer.ContactNumber : oldCustomer.ContactNumber;
            copyObject.Address = !string.IsNullOrEmpty(newCustomer.Address) ? newCustomer.Address : oldCustomer.Address;
            copyObject.Email = !string.IsNullOrEmpty(newCustomer.Email) ? newCustomer.Email : oldCustomer.Email;
            copyObject.Comments = !string.IsNullOrEmpty(newCustomer.Comments) ? newCustomer.Comments : oldCustomer.Comments;
            copyObject.TaxCode = !string.IsNullOrEmpty(newCustomer.TaxCode) ? newCustomer.TaxCode : oldCustomer.TaxCode;
            return copyObject;
        }
    }
}
