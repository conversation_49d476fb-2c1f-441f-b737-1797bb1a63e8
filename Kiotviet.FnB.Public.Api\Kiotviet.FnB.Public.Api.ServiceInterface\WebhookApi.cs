﻿using System.Collections.Generic;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Services.Interface;
using System.Linq;
using System.Data.Entity;
using System.Threading.Tasks;
using ServiceStack;
using KiotViet.Persistence;
using KiotViet.FnB.Public.Api.WebhookHelper;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class WebhookApi : BaseApi
    {
        public IWebHookService WebhookService { get; set; }

        public async Task<SyncDataSources<WebhookDto>> Get(WebhookList req)
        {
            var webhooks = WebhookService.GetAll().Where(w => w.RetailerId == CurrentRetailerId);
            if (req.Orderby == null) webhooks = webhooks.OrderByDescending(x => x.CreatedDate);
            var retval = await webhooks.Take(req).Select(x => new WebhookDto { Id = x.Id, Description = x.Description, IsActive = x.IsActive, ModifiedDate = x.ModifiedDate ?? x.CreatedDate, RetailerId = x.RetailerId, Type = x.Type, Url = x.Url }).ToListAsync();
            var ds = new SyncDataSources<WebhookDto>
            {
                Data = retval,
                Total = await webhooks.CountAsync()
            };
            return ds;
        }

        public async Task<WebhookDto> Get(GetWebhook req)
        {
            var x = await WebhookService.GetByIdAsync(req.Id);
            return x?.ConvertTo<WebhookDto>();
        }


        public async Task<WebhookDto> Post(CreateOrUpdateWebhook req)
        {
            if (req.Webhook == null) return null;
            var obj = req.Webhook.ConvertTo<Webhook>();
            obj.RetailerId = CurrentRetailerId;
            await WebhookService.Validate(obj);
            var filter = $"{obj.Type}.{CurrentRetailerId}";
            await WebhookServices.CreateFilter(filter, $"filter {obj.Type} for {CurrentRetailerCode}");
            var wbhook = new WebhookHelper.Model.Webhook
            {
                Description = obj.Description,
                Filters = new List<string> { filter },
                WebHookUri = obj.Url
            };
            var whreponse = await WebhookServices.RegisterWebhook(wbhook);
            if (string.IsNullOrEmpty(whreponse?.Id)) return null;
            obj.WebhookId = whreponse.Id;
            obj.isRunning = false;
            var webhook = await WebhookService.CreateOrUpdateAsync(obj);
            return webhook?.ConvertTo<WebhookDto>();
        }


        public async Task<bool> Delete(RemoveWebhook req)
        {
            var wbhook = await WebhookService.GetByIdAsync(req.Id);
            if (wbhook == null) return false;
            await WebhookServices.DeleteFilter(wbhook.Type);
            await WebhookServices.UnRegisterWebhook(wbhook.WebhookId);
            return await WebhookService.RemoveAsync(req.Id);

        }
    }
}