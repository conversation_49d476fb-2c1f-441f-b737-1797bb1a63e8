function clippyCopiedCallback(){$("#api_key_copied").fadeIn().delay(1e3).fadeOut()}$(function(){$.fn.vAlign=function(){return this.each(function(){var e=$(this).height(),t=$(this).parent().height(),n=(t-e)/2;$(this).css("margin-top",n)})},$.fn.stretchFormtasticInputWidthToParent=function(){return this.each(function(){var e=$(this).closest("form").innerWidth(),t=parseInt($(this).closest("form").css("padding-left"),10)+parseInt($(this).closest("form").css("padding-right"),10),n=parseInt($(this).css("padding-left"),10)+parseInt($(this).css("padding-right"),10);$(this).css("width",e-t-n)})},$("form.formtastic li.string input, form.formtastic textarea").stretchFormtasticInputWidthToParent(),$("ul.downplayed li div.content p").vAlign(),$("form.sandbox").submit(function(){var e=!0;return $(this).find("input.required").each(function(){$(this).removeClass("error"),""==$(this).val()&&($(this).addClass("error"),$(this).wiggle(),e=!1)}),e})}),log=function(){log.history=log.history||[],log.history.push(arguments),this.console&&console.log(Array.prototype.slice.call(arguments)[0])},Function.prototype.bind&&console&&"object"==typeof console.log&&["log","info","warn","error","assert","dir","clear","profile","profileEnd"].forEach(function(e){console[e]=this.bind(console[e],console)},Function.prototype.call);var Docs={shebang:function(){var e=$.param.fragment().split("/");switch(e.shift(),e.length){case 1:var t="resource_"+e[0];Docs.expandEndpointListForResource(e[0]),$("#"+t).slideto({highlight:!1});break;case 2:Docs.expandEndpointListForResource(e[0]),$("#"+t).slideto({highlight:!1});var n=e.join("_"),a=n+"_content";Docs.expandOperation($("#"+a)),$("#"+n).slideto({highlight:!1})}},toggleEndpointListForResource:function(e){var t=$("li#resource_"+Docs.escapeResourceName(e)+" ul.endpoints");t.is(":visible")?Docs.collapseEndpointListForResource(e):Docs.expandEndpointListForResource(e)},expandEndpointListForResource:function(e){var e=Docs.escapeResourceName(e);if(""==e)return void $(".resource ul.endpoints").slideDown();$("li#resource_"+e).addClass("active");var t=$("li#resource_"+e+" ul.endpoints");t.slideDown()},collapseEndpointListForResource:function(e){var e=Docs.escapeResourceName(e);if(""==e)return void $(".resource ul.endpoints").slideUp();$("li#resource_"+e).removeClass("active");var t=$("li#resource_"+e+" ul.endpoints");t.slideUp()},expandOperationsForResource:function(e){return Docs.expandEndpointListForResource(e),""==e?void $(".resource ul.endpoints li.operation div.content").slideDown():void $("li#resource_"+Docs.escapeResourceName(e)+" li.operation div.content").each(function(){Docs.expandOperation($(this))})},collapseOperationsForResource:function(e){return Docs.expandEndpointListForResource(e),""==e?void $(".resource ul.endpoints li.operation div.content").slideUp():void $("li#resource_"+Docs.escapeResourceName(e)+" li.operation div.content").each(function(){Docs.collapseOperation($(this))})},escapeResourceName:function(e){return e.replace(/[!"#$%&'()*+,.\/:;<=>?@\[\\\]\^`{|}~]/g,"\\$&")},expandOperation:function(e){e.slideDown()},collapseOperation:function(e){e.slideUp()}};this.Handlebars=this.Handlebars||{},this.Handlebars.templates=this.Handlebars.templates||{},this.Handlebars.templates.apikey_button_view=Handlebars.template({compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"<!--div class='auth_button' id='apikey_button'><img class='auth_icon' alt='apply api key' src='images/apikey.jpeg'></div-->\n<div class='auth_container' id='apikey_container'>\n  <div class='key_input_container'>\n    <div class='auth_label'>"+l((s=null!=(s=t.keyName||(null!=e?e.keyName:e))?s:r,typeof s===i?s.call(e,{name:"keyName",hash:{},data:a}):s))+'</div>\n    <input placeholder="api_key" class="auth_input" id="input_apiKey_entry" name="apiKey" type="text"/>\n    <div class=\'auth_submit\'><a class=\'auth_submit_button\' id="apply_api_key" href="#">apply</a></div>\n  </div>\n</div>\n\n'},useData:!0});var SwaggerUi,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;SwaggerUi=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.dom_id="swagger_ui",t.prototype.options=null,t.prototype.api=null,t.prototype.headerView=null,t.prototype.mainView=null,t.prototype.initialize=function(e){return null==e&&(e={}),null!=e.dom_id&&(this.dom_id=e.dom_id,delete e.dom_id),null==e.supportedSubmitMethods&&(e.supportedSubmitMethods=["get","put","post","delete","head","options","patch"]),null==$("#"+this.dom_id)&&$("body").append('<div id="'+this.dom_id+'"></div>'),this.options=e,this.options.success=function(e){return function(){return e.render()}}(this),this.options.progress=function(e){return function(t){return e.showMessage(t)}}(this),this.options.failure=function(e){return function(t){return e.onLoadFailure(t)}}(this),this.headerView=new HeaderView({el:$("#header")}),this.headerView.on("update-swagger-ui",function(e){return function(t){return e.updateSwaggerUi(t)}}(this))},t.prototype.setOption=function(e,t){return this.options[e]=t},t.prototype.getOption=function(e){return this.options[e]},t.prototype.updateSwaggerUi=function(e){return this.options.url=e.url,this.load()},t.prototype.load=function(){var e,t;return null!=(t=this.mainView)&&t.clear(),e=this.options.url,e&&0!==e.indexOf("http")&&(e=this.buildUrl(window.location.href.toString(),e)),this.options.url=e,this.headerView.update(e),this.api=new SwaggerClient(this.options)},t.prototype.collapseAll=function(){return Docs.collapseEndpointListForResource("")},t.prototype.listAll=function(){return Docs.collapseOperationsForResource("")},t.prototype.expandAll=function(){return Docs.expandOperationsForResource("")},t.prototype.render=function(){switch(this.showMessage("Finished Loading Resource Information. Rendering Swagger UI..."),this.mainView=new MainView({model:this.api,el:$("#"+this.dom_id),swaggerOptions:this.options}).render(),this.showMessage(),this.options.docExpansion){case"full":this.expandAll();break;case"list":this.listAll()}return this.renderGFM(),this.options.onComplete&&this.options.onComplete(this.api,this),setTimeout(function(){return function(){return Docs.shebang()}}(this),100)},t.prototype.buildUrl=function(e,t){var n,a;return 0===t.indexOf("/")?(a=e.split("/"),e=a[0]+"//"+a[2],e+t):(n=e.length,e.indexOf("?")>-1&&(n=Math.min(n,e.indexOf("?"))),e.indexOf("#")>-1&&(n=Math.min(n,e.indexOf("#"))),e=e.substring(0,n),-1!==e.indexOf("/",e.length-1)?e+t:e+"/"+t)},t.prototype.showMessage=function(e){return null==e&&(e=""),$("#message-bar").removeClass("message-fail"),$("#message-bar").addClass("message-success"),$("#message-bar").html(e)},t.prototype.onLoadFailure=function(e){var t;return null==e&&(e=""),$("#message-bar").removeClass("message-success"),$("#message-bar").addClass("message-fail"),t=$("#message-bar").html(e),null!=this.options.onFailure&&this.options.onFailure(e),t},t.prototype.renderGFM=function(e){return null==e&&(e=""),$(".markdown").each(function(){return $(this).html(marked($(this).html()))})},t}(Backbone.Router),window.SwaggerUi=SwaggerUi,this.Handlebars.templates.basic_auth_button_view=Handlebars.template({compiler:[6,">= 2.0.0-beta.1"],main:function(){return'<div class=\'auth_button\' id=\'basic_auth_button\'><img class=\'auth_icon\' src=\'images/password.jpeg\'></div>\n<div class=\'auth_container\' id=\'basic_auth_container\'>\n  <div class=\'key_input_container\'>\n    <div class="auth_label">Username</div>\n    <input placeholder="username" class="auth_input" id="input_username" name="username" type="text"/>\n    <div class="auth_label">Password</div>\n    <input placeholder="password" class="auth_input" id="input_password" name="password" type="password"/>\n    <div class=\'auth_submit\'><a class=\'auth_submit_button\' id="apply_basic_auth" href="#">apply</a></div>\n  </div>\n</div>\n\n'},useData:!0}),Handlebars.registerHelper("sanitize",function(e){return e=e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,""),new Handlebars.SafeString(e)}),this.Handlebars.templates.content_type=Handlebars.template({1:function(e,t,n,a){var s,i="";return s=t.each.call(e,null!=e?e.produces:e,{name:"each",hash:{},fn:this.program(2,a),inverse:this.noop,data:a}),null!=s&&(i+=s),i},2:function(e){var t,n=this.lambda,a='	<option value="';return t=n(e,e),null!=t&&(a+=t),a+='">',t=n(e,e),null!=t&&(a+=t),a+"</option>\n"},4:function(){return'  <option value="application/json">application/json</option>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i='<label for="contentType"></label>\n<select name="contentType">\n';return s=t["if"].call(e,null!=e?e.produces:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.program(4,a),data:a}),null!=s&&(i+=s),i+"</select>\n"},useData:!0});var ApiKeyButton,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;ApiKeyButton=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(){},t.prototype.render=function(){var e;return e=this.template(),$(this.el).html(e(this.model)),this},t.prototype.events={"click #apikey_button":"toggleApiKeyContainer","click #apply_api_key":"applyApiKey"},t.prototype.applyApiKey=function(){var e;return window.authorizations.add(this.model.name,new ApiKeyAuthorization(this.model.name,$("#input_apiKey_entry").val(),this.model["in"])),window.swaggerUi.load(),e=$("#apikey_container").show()},t.prototype.toggleApiKeyContainer=function(){var e;return $("#apikey_container").length>0?(e=$("#apikey_container").first(),e.is(":visible")?e.hide():($(".auth_container").hide(),e.show())):void 0},t.prototype.template=function(){return Handlebars.templates.apikey_button_view},t}(Backbone.View),this.Handlebars.templates.main=Handlebars.template({1:function(e,t,n,a){var s,i=this.lambda,r=this.escapeExpression,l='  <div class="info_title">'+r(i(null!=(s=null!=e?e.info:e)?s.title:s,e))+'</div>\n  <div class="info_description markdown">';return s=i(null!=(s=null!=e?e.info:e)?s.description:s,e),null!=s&&(l+=s),l+="</div>\n  ",s=t["if"].call(e,null!=(s=null!=e?e.info:e)?s.termsOfServiceUrl:s,{name:"if",hash:{},fn:this.program(2,a),inverse:this.noop,data:a}),null!=s&&(l+=s),l+="\n  ",s=t["if"].call(e,null!=(s=null!=(s=null!=e?e.info:e)?s.contact:s)?s.name:s,{name:"if",hash:{},fn:this.program(4,a),inverse:this.noop,data:a}),null!=s&&(l+=s),l+="\n  ",s=t["if"].call(e,null!=(s=null!=(s=null!=e?e.info:e)?s.contact:s)?s.url:s,{name:"if",hash:{},fn:this.program(6,a),inverse:this.noop,data:a}),null!=s&&(l+=s),l+="\n  ",s=t["if"].call(e,null!=(s=null!=(s=null!=e?e.info:e)?s.contact:s)?s.email:s,{name:"if",hash:{},fn:this.program(8,a),inverse:this.noop,data:a}),null!=s&&(l+=s),l+="\n  ",s=t["if"].call(e,null!=(s=null!=e?e.info:e)?s.license:s,{name:"if",hash:{},fn:this.program(10,a),inverse:this.noop,data:a}),null!=s&&(l+=s),l+"\n"},2:function(e){var t,n=this.lambda,a=this.escapeExpression;return'<div class="info_tos"><a href="'+a(n(null!=(t=null!=e?e.info:e)?t.termsOfServiceUrl:t,e))+'">Terms of service</a></div>'},4:function(e){var t,n=this.lambda,a=this.escapeExpression;return"<div class='info_name'>Created by "+a(n(null!=(t=null!=(t=null!=e?e.info:e)?t.contact:t)?t.name:t,e))+"</div>"},6:function(e){var t,n=this.lambda,a=this.escapeExpression;return"<div class='info_url'>See more at <a href=\""+a(n(null!=(t=null!=(t=null!=e?e.info:e)?t.contact:t)?t.url:t,e))+'">'+a(n(null!=(t=null!=(t=null!=e?e.info:e)?t.contact:t)?t.url:t,e))+"</a></div>"},8:function(e){var t,n=this.lambda,a=this.escapeExpression;return"<div class='info_email'><a href=\"mailto:"+a(n(null!=(t=null!=(t=null!=e?e.info:e)?t.contact:t)?t.email:t,e))+"?subject="+a(n(null!=(t=null!=e?e.info:e)?t.title:t,e))+'">Contact the developer</a></div>'},10:function(e){var t,n=this.lambda,a=this.escapeExpression;return"<div class='info_license'><a href='"+a(n(null!=(t=null!=(t=null!=e?e.info:e)?t.license:t)?t.url:t,e))+"'>"+a(n(null!=(t=null!=(t=null!=e?e.info:e)?t.license:t)?t.name:t,e))+"</a></div>"},12:function(e){var t,n=this.lambda,a=this.escapeExpression;return'    , <span style="font-variant: small-caps">api version</span>: '+a(n(null!=(t=null!=e?e.info:e)?t.version:t,e))+"\n    "},14:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return'    <span style="float:right"><a href="'+l((s=null!=(s=t.validatorUrl||(null!=e?e.validatorUrl:e))?s:r,typeof s===i?s.call(e,{name:"validatorUrl",hash:{},data:a}):s))+"/debug?url="+l((s=null!=(s=t.url||(null!=e?e.url:e))?s:r,typeof s===i?s.call(e,{name:"url",hash:{},data:a}):s))+'"><img id="validator" src="'+l((s=null!=(s=t.validatorUrl||(null!=e?e.validatorUrl:e))?s:r,typeof s===i?s.call(e,{name:"validatorUrl",hash:{},data:a}):s))+"?url="+l((s=null!=(s=t.url||(null!=e?e.url:e))?s:r,typeof s===i?s.call(e,{name:"url",hash:{},data:a}):s))+'"></a>\n    </span>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p="<div class='info' id='api_info'>\n";return s=t["if"].call(e,null!=e?e.info:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.noop,data:a}),null!=s&&(p+=s),p+="</div>\n<div class='container' id='resources_container'>\n  <ul id='resources'></ul>\n\n  <div class=\"footer\">\n    <br>\n    <br>\n    <h4 style=\"color: #999\">[ <span style=\"font-variant: small-caps\">base url</span>: "+o((i=null!=(i=t.basePath||(null!=e?e.basePath:e))?i:l,typeof i===r?i.call(e,{name:"basePath",hash:{},data:a}):i))+"\n",s=t["if"].call(e,null!=(s=null!=e?e.info:e)?s.version:s,{name:"if",hash:{},fn:this.program(12,a),inverse:this.noop,data:a}),null!=s&&(p+=s),p+="]\n",s=t["if"].call(e,null!=e?e.validatorUrl:e,{name:"if",hash:{},fn:this.program(14,a),inverse:this.noop,data:a}),null!=s&&(p+=s),p+"    </h4>\n    </div>\n</div>\n"},useData:!0});var BasicAuthButton,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;BasicAuthButton=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(){},t.prototype.render=function(){var e;return e=this.template(),$(this.el).html(e(this.model)),this},t.prototype.events={"click #basic_auth_button":"togglePasswordContainer","click #apply_basic_auth":"applyPassword"},t.prototype.applyPassword=function(){var e,t,n;return n=$(".input_username").val(),t=$(".input_password").val(),window.authorizations.add(this.model.type,new PasswordAuthorization("basic",n,t)),window.swaggerUi.load(),e=$("#basic_auth_container").hide()},t.prototype.togglePasswordContainer=function(){var e;return $("#basic_auth_container").length>0?(e=$("#basic_auth_container").show(),e.is(":visible")?e.slideUp():($(".auth_container").hide(),e.show())):void 0},t.prototype.template=function(){return Handlebars.templates.basic_auth_button_view},t}(Backbone.View),this.Handlebars.templates.operation=Handlebars.template({1:function(){return"deprecated"},3:function(){return"            <h4>Warning: Deprecated</h4>\n"},5:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o='        <h4>Implementation Notes</h4>\n        <p class="markdown">';return i=null!=(i=t.description||(null!=e?e.description:e))?i:l,s=typeof i===r?i.call(e,{name:"description",hash:{},data:a}):i,null!=s&&(o+=s),o+"</p>\n"},7:function(){return'        <div class="auth">\n        <span class="api-ic ic-error"></span>'},9:function(e,t,n,a){var s,i='          <div id="api_information_panel" style="top: 526px; left: 776px; display: none;">\n';return s=t.each.call(e,e,{name:"each",hash:{},fn:this.program(10,a),inverse:this.noop,data:a}),null!=s&&(i+=s),i+"          </div>\n"},10:function(e){var t,n=this.lambda,a=this.escapeExpression,s="            <div title='";return t=n(null!=e?e.description:e,e),null!=t&&(s+=t),s+"'>"+a(n(null!=e?e.scope:e,e))+"</div>\n"},12:function(){return"</div>"},14:function(){return'        <div class=\'access\'>\n          <span class="api-ic ic-off" title="click to authenticate"></span>\n        </div>\n'},16:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"          <h4>Response Class (Status "+l((s=null!=(s=t.successCode||(null!=e?e.successCode:e))?s:r,typeof s===i?s.call(e,{name:"successCode",hash:{},data:a}):s))+')</h4>\n          <p><span class="model-signature" /></p>\n          <br/>\n          <div class="response-content-type" />\n'},18:function(){return'          <h4>Parameters</h4>\n          <table class=\'fullwidth\'>\n          <thead>\n            <tr>\n            <th style="width: 100px; max-width: 100px">Parameter</th>\n            <th style="width: 310px; max-width: 310px">Value</th>\n            <th style="width: 200px; max-width: 200px">Description</th>\n            <th style="width: 100px; max-width: 100px">Parameter Type</th>\n            <th style="width: 220px; max-width: 230px">Data Type</th>\n            </tr>\n          </thead>\n          <tbody class="operation-params">\n\n          </tbody>\n          </table>\n'},20:function(){return"          <div style='margin:0;padding:0;display:inline'></div>\n          <h4>Response Messages</h4>\n          <table class='fullwidth'>\n            <thead>\n            <tr>\n              <th>HTTP Status Code</th>\n              <th>Reason</th>\n              <th>Response Model</th>\n            </tr>\n            </thead>\n            <tbody class=\"operation-status\">\n            \n            </tbody>\n          </table>\n"},22:function(){return""},24:function(){return"          <div class='sandbox_header'>\n            <input class='submit' name='commit' type='button' value='Try it out!' />\n            <a href='#' class='response_hider' style='display:none'>Hide Response</a>\n            <span class='response_throbber' style='display:none'></span>\n          </div>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r,l="function",o=t.helperMissing,p=this.escapeExpression,u=t.blockHelperMissing,h="\n  <ul class='operations' >\n    <li class='"+p((i=null!=(i=t.method||(null!=e?e.method:e))?i:o,typeof i===l?i.call(e,{name:"method",hash:{},data:a}):i))+" operation' id='"+p((i=null!=(i=t.parentId||(null!=e?e.parentId:e))?i:o,typeof i===l?i.call(e,{name:"parentId",hash:{},data:a}):i))+"_"+p((i=null!=(i=t.nickname||(null!=e?e.nickname:e))?i:o,typeof i===l?i.call(e,{name:"nickname",hash:{},data:a}):i))+"'>\n      <div class='heading'>\n        <h3>\n          <span class='http_method'>\n          <a href='#!/"+p((i=null!=(i=t.parentId||(null!=e?e.parentId:e))?i:o,typeof i===l?i.call(e,{name:"parentId",hash:{},data:a}):i))+"/"+p((i=null!=(i=t.nickname||(null!=e?e.nickname:e))?i:o,typeof i===l?i.call(e,{name:"nickname",hash:{},data:a}):i))+'\' class="toggleOperation">'+p((i=null!=(i=t.method||(null!=e?e.method:e))?i:o,typeof i===l?i.call(e,{name:"method",hash:{},data:a}):i))+"</a>\n          </span>\n          <span class='path'>\n          <a href='#!/"+p((i=null!=(i=t.parentId||(null!=e?e.parentId:e))?i:o,typeof i===l?i.call(e,{name:"parentId",hash:{},data:a}):i))+"/"+p((i=null!=(i=t.nickname||(null!=e?e.nickname:e))?i:o,typeof i===l?i.call(e,{name:"nickname",hash:{},data:a}):i))+"' class=\"toggleOperation ";return s=t["if"].call(e,null!=e?e.deprecated:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.noop,data:a}),null!=s&&(h+=s),h+='">'+p((i=null!=(i=t.path||(null!=e?e.path:e))?i:o,typeof i===l?i.call(e,{name:"path",hash:{},data:a}):i))+"</a>\n          </span>\n        </h3>\n        <ul class='options'>\n          <li>\n          <a href='#!/"+p((i=null!=(i=t.parentId||(null!=e?e.parentId:e))?i:o,typeof i===l?i.call(e,{name:"parentId",hash:{},data:a}):i))+"/"+p((i=null!=(i=t.nickname||(null!=e?e.nickname:e))?i:o,typeof i===l?i.call(e,{name:"nickname",hash:{},data:a}):i))+'\' class="toggleOperation">',i=null!=(i=t.summary||(null!=e?e.summary:e))?i:o,s=typeof i===l?i.call(e,{name:"summary",hash:{},data:a}):i,null!=s&&(h+=s),h+="</a>\n          </li>\n        </ul>\n      </div>\n      <div class='content' id='"+p((i=null!=(i=t.parentId||(null!=e?e.parentId:e))?i:o,typeof i===l?i.call(e,{name:"parentId",hash:{},data:a}):i))+"_"+p((i=null!=(i=t.nickname||(null!=e?e.nickname:e))?i:o,typeof i===l?i.call(e,{name:"nickname",hash:{},data:a}):i))+"_content' style='display:none'>\n",s=t["if"].call(e,null!=e?e.deprecated:e,{name:"if",hash:{},fn:this.program(3,a),inverse:this.noop,data:a}),null!=s&&(h+=s),s=t["if"].call(e,null!=e?e.description:e,{name:"if",hash:{},fn:this.program(5,a),inverse:this.noop,data:a}),null!=s&&(h+=s),i=null!=(i=t.oauth||(null!=e?e.oauth:e))?i:o,r={name:"oauth",hash:{},fn:this.program(7,a),inverse:this.noop,data:a},s=typeof i===l?i.call(e,r):i,t.oauth||(s=u.call(e,s,r)),null!=s&&(h+=s),h+="\n",s=t.each.call(e,null!=e?e.oauth:e,{name:"each",hash:{},fn:this.program(9,a),inverse:this.noop,data:a}),null!=s&&(h+=s),h+="        ",i=null!=(i=t.oauth||(null!=e?e.oauth:e))?i:o,r={name:"oauth",hash:{},fn:this.program(12,a),inverse:this.noop,data:a},s=typeof i===l?i.call(e,r):i,t.oauth||(s=u.call(e,s,r)),null!=s&&(h+=s),h+="\n",i=null!=(i=t.oauth||(null!=e?e.oauth:e))?i:o,r={name:"oauth",hash:{},fn:this.program(14,a),inverse:this.noop,data:a},s=typeof i===l?i.call(e,r):i,t.oauth||(s=u.call(e,s,r)),null!=s&&(h+=s),s=t["if"].call(e,null!=e?e.type:e,{name:"if",hash:{},fn:this.program(16,a),inverse:this.noop,data:a}),null!=s&&(h+=s),h+="        <form accept-charset='UTF-8' class='sandbox'>\n          <div style='margin:0;padding:0;display:inline'></div>\n",s=t["if"].call(e,null!=e?e.parameters:e,{name:"if",hash:{},fn:this.program(18,a),inverse:this.noop,data:a}),null!=s&&(h+=s),s=t["if"].call(e,null!=e?e.responseMessages:e,{name:"if",hash:{},fn:this.program(20,a),inverse:this.noop,data:a}),null!=s&&(h+=s),s=t["if"].call(e,null!=e?e.isReadOnly:e,{name:"if",hash:{},fn:this.program(22,a),inverse:this.program(24,a),data:a}),null!=s&&(h+=s),h+"        </form>\n        <div class='response' style='display:none'>\n          <h4>Request URL</h4>\n          <div class='block request_url'></div>\n          <h4>Response Body</h4>\n          <div class='block response_body'></div>\n          <h4>Response Code</h4>\n          <div class='block response_code'></div>\n          <h4>Response Headers</h4>\n          <div class='block response_headers'></div>\n        </div>\n      </div>\n    </li>\n  </ul>\n"},useData:!0});var ContentTypeView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;ContentTypeView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(){},t.prototype.render=function(){var e;return e=this.template(),$(this.el).html(e(this.model)),$("label[for=contentType]",$(this.el)).text("Response Content Type"),this},t.prototype.template=function(){return Handlebars.templates.content_type},t}(Backbone.View),this.Handlebars.templates.param=Handlebars.template({1:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(2,a),inverse:this.program(4,a),data:a}),null!=s&&(i+=s),i},2:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return'			<input type="file" name=\''+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+'\'/>\n			<div class="parameter-content-type" />\n'},4:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(5,a),inverse:this.program(7,a),data:a}),null!=s&&(i+=s),i},5:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<textarea class='body-textarea' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"'>"+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+'</textarea>\n        <br />\n        <div class="parameter-content-type" />\n'},7:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<textarea class='body-textarea' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+'\'></textarea>\n				<br />\n				<div class="parameter-content-type" />\n'},9:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(2,a),inverse:this.program(10,a),data:a}),null!=s&&(i+=s),i},10:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(11,a),inverse:this.program(13,a),data:a}),null!=s&&(i+=s),i},11:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<input class='parameter' minlength='0' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"' placeholder='' type='text' value='"+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+"'/>\n"},13:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<input class='parameter' minlength='0' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"' placeholder='' type='text' value=''/>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p="<td class='code'>"+o((i=null!=(i=t.name||(null!=e?e.name:e))?i:l,typeof i===r?i.call(e,{name:"name",hash:{},data:a}):i))+"</td>\n<td>\n\n";return s=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.program(9,a),data:a}),null!=s&&(p+=s),p+='\n</td>\n<td class="markdown">',i=null!=(i=t.description||(null!=e?e.description:e))?i:l,s=typeof i===r?i.call(e,{name:"description",hash:{},data:a}):i,null!=s&&(p+=s),p+="</td>\n<td>",i=null!=(i=t.paramType||(null!=e?e.paramType:e))?i:l,s=typeof i===r?i.call(e,{name:"paramType",hash:{},data:a}):i,null!=s&&(p+=s),p+'</td>\n<td>\n	<span class="model-signature"></span>\n</td>\n'},useData:!0});var HeaderView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;HeaderView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.events={"click #show-pet-store-icon":"showPetStore","click #show-wordnik-dev-icon":"showWordnikDev","click #explore":"showCustom","keyup #input_baseUrl":"showCustomOnKeyup","keyup #input_apiKey":"showCustomOnKeyup"},t.prototype.initialize=function(){},t.prototype.showPetStore=function(){return this.trigger("update-swagger-ui",{url:"http://petstore.swagger.wordnik.com/api/api-docs"})},t.prototype.showWordnikDev=function(){return this.trigger("update-swagger-ui",{url:"http://api.wordnik.com/v4/resources.json"})},t.prototype.showCustomOnKeyup=function(e){return 13===e.keyCode?this.showCustom():void 0},t.prototype.showCustom=function(e){return null!=e&&e.preventDefault(),this.trigger("update-swagger-ui",{url:$("#input_baseUrl").val(),apiKey:$("#input_apiKey").val()})},t.prototype.update=function(e,t,n){return null==n&&(n=!1),$("#input_baseUrl").val(e),n?this.trigger("update-swagger-ui",{url:e}):void 0},t}(Backbone.View),this.Handlebars.templates.param_list=Handlebars.template({1:function(){return" multiple='multiple'"},3:function(){return""},5:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(3,a),inverse:this.program(6,a),data:a}),null!=s&&(i+=s),i},6:function(e,t,n,a){var s,i=t.helperMissing,r="";return s=(t.isArray||e&&e.isArray||i).call(e,e,{name:"isArray",hash:{},fn:this.program(3,a),inverse:this.program(7,a),data:a}),null!=s&&(r+=s),r},7:function(){return"          <option selected=\"\" value=''></option>\n"},9:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e.isDefault:e,{name:"if",hash:{},fn:this.program(10,a),inverse:this.program(12,a),data:a}),null!=s&&(i+=s),i},10:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return'        <option selected="" value=\''+l((s=null!=(s=t.value||(null!=e?e.value:e))?s:r,typeof s===i?s.call(e,{name:"value",hash:{},data:a}):s))+"'>"+l((s=null!=(s=t.value||(null!=e?e.value:e))?s:r,typeof s===i?s.call(e,{name:"value",hash:{},data:a}):s))+" (default)</option>\n"},12:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"        <option value='"+l((s=null!=(s=t.value||(null!=e?e.value:e))?s:r,typeof s===i?s.call(e,{name:"value",hash:{},data:a}):s))+"'>"+l((s=null!=(s=t.value||(null!=e?e.value:e))?s:r,typeof s===i?s.call(e,{name:"value",hash:{},data:a}):s))+"</option>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p="<td class='code'>"+o((i=null!=(i=t.name||(null!=e?e.name:e))?i:l,typeof i===r?i.call(e,{name:"name",hash:{},data:a}):i))+"</td>\n<td>\n  <select ";return s=(t.isArray||e&&e.isArray||l).call(e,e,{name:"isArray",hash:{},fn:this.program(1,a),inverse:this.noop,data:a}),null!=s&&(p+=s),p+=" class='parameter' name='"+o((i=null!=(i=t.name||(null!=e?e.name:e))?i:l,typeof i===r?i.call(e,{name:"name",hash:{},data:a}):i))+"'>\n",s=t["if"].call(e,null!=e?e.required:e,{name:"if",hash:{},fn:this.program(3,a),inverse:this.program(5,a),data:a}),null!=s&&(p+=s),s=t.each.call(e,null!=(s=null!=e?e.allowableValues:e)?s.descriptiveValues:s,{name:"each",hash:{},fn:this.program(9,a),inverse:this.noop,data:a}),null!=s&&(p+=s),p+='  </select>\n</td>\n<td class="markdown">',i=null!=(i=t.description||(null!=e?e.description:e))?i:l,s=typeof i===r?i.call(e,{name:"description",hash:{},data:a}):i,null!=s&&(p+=s),p+="</td>\n<td>",i=null!=(i=t.paramType||(null!=e?e.paramType:e))?i:l,s=typeof i===r?i.call(e,{name:"paramType",hash:{},data:a}):i,null!=s&&(p+=s),p+'</td>\n<td><span class="model-signature"></span></td>'},useData:!0});var MainView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;MainView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}var n;return __extends(t,e),n={alpha:function(e,t){return e.path.localeCompare(t.path)},method:function(e,t){return e.method.localeCompare(t.method)}},t.prototype.initialize=function(e){var t,n,a,s;null==e&&(e={}),this.model.auths=[],s=this.model.securityDefinitions;for(n in s)a=s[n],t={name:n,type:a.type,value:a},this.model.auths.push(t);return"2.0"===this.model.swaggerVersion?this.model.validatorUrl="validatorUrl"in e.swaggerOptions?e.swaggerOptions.validatorUrl:this.model.url.indexOf("localhost")>0?null:"http://online.swagger.io/validator":void 0},t.prototype.render=function(){var e,t,n,a,s,i,r,l,o,p;if(this.model.securityDefinitions)for(s in this.model.securityDefinitions)e=this.model.securityDefinitions[s],"apiKey"===e.type&&0===$("#apikey_button").length&&(t=new ApiKeyButton({model:e}).render().el,$(".auth_main_container").append(t)),"basicAuth"===e.type&&0===$("#basic_auth_button").length&&(t=new BasicAuthButton({model:e}).render().el,$(".auth_main_container").append(t));for($(this.el).html(Handlebars.templates.main(this.model)),r={},n=0,p=this.model.apisArray,l=0,o=p.length;o>l;l++){for(i=p[l],a=i.name;"undefined"!=typeof r[a];)a=a+"_"+n,n+=1;
i.id=a,r[a]=i,this.addResource(i,this.model.auths)}return $(".propWrap").hover(function(){return $(".optionsWrapper",$(this)).show()},function(){return $(".optionsWrapper",$(this)).hide()}),this},t.prototype.addResource=function(e,t){var n;return e.id=e.id.replace(/\s/g,"_"),n=new ResourceView({model:e,tagName:"li",id:"resource_"+e.id,className:"resource",auths:t,swaggerOptions:this.options.swaggerOptions}),$("#resources").append(n.render().el)},t.prototype.clear=function(){return $(this.el).html("")},t}(Backbone.View),this.Handlebars.templates.param_readonly=Handlebars.template({1:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"        <textarea class='body-textarea' readonly='readonly' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"'>"+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+"</textarea>\n"},3:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(4,a),inverse:this.program(6,a),data:a}),null!=s&&(i+=s),i},4:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"            "+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+"\n"},6:function(){return"            (empty)\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p="<td class='code'>"+o((i=null!=(i=t.name||(null!=e?e.name:e))?i:l,typeof i===r?i.call(e,{name:"name",hash:{},data:a}):i))+"</td>\n<td>\n";return s=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.program(3,a),data:a}),null!=s&&(p+=s),p+='</td>\n<td class="markdown">',i=null!=(i=t.description||(null!=e?e.description:e))?i:l,s=typeof i===r?i.call(e,{name:"description",hash:{},data:a}):i,null!=s&&(p+=s),p+="</td>\n<td>",i=null!=(i=t.paramType||(null!=e?e.paramType:e))?i:l,s=typeof i===r?i.call(e,{name:"paramType",hash:{},data:a}):i,null!=s&&(p+=s),p+'</td>\n<td><span class="model-signature"></span></td>\n'},useData:!0}),this.Handlebars.templates.param_readonly_required=Handlebars.template({1:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"        <textarea class='body-textarea'  readonly='readonly' placeholder='(required)' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"'>"+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+"</textarea>\n"},3:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(4,a),inverse:this.program(6,a),data:a}),null!=s&&(i+=s),i},4:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"            "+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+"\n"},6:function(){return"            (empty)\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p="<td class='code required'>"+o((i=null!=(i=t.name||(null!=e?e.name:e))?i:l,typeof i===r?i.call(e,{name:"name",hash:{},data:a}):i))+"</td>\n<td>\n";return s=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.program(3,a),data:a}),null!=s&&(p+=s),p+='</td>\n<td class="markdown">',i=null!=(i=t.description||(null!=e?e.description:e))?i:l,s=typeof i===r?i.call(e,{name:"description",hash:{},data:a}):i,null!=s&&(p+=s),p+="</td>\n<td>",i=null!=(i=t.paramType||(null!=e?e.paramType:e))?i:l,s=typeof i===r?i.call(e,{name:"paramType",hash:{},data:a}):i,null!=s&&(p+=s),p+'</td>\n<td><span class="model-signature"></span></td>\n'},useData:!0});var OperationView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;OperationView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.invocationUrl=null,t.prototype.events={"submit .sandbox":"submitOperation","click .submit":"submitOperation","click .response_hider":"hideResponse","click .toggleOperation":"toggleOperationContent","mouseenter .api-ic":"mouseEnter","mouseout .api-ic":"mouseExit"},t.prototype.initialize=function(e){return null==e&&(e={}),this.auths=e.auths,this.parentId=this.model.parentId,this.nickname=this.model.nickname,this},t.prototype.mouseEnter=function(e){var t,n,a,s,i,r,l,o,p,u;return t=$(this.el).find(".content"),p=e.pageX,u=e.pageY,r=$(window).scrollLeft(),l=$(window).scrollTop(),s=r+$(window).width(),i=l+$(window).height(),o=t.width(),n=t.height(),p+o>s&&(p=s-o),r>p&&(p=r),u+n>i&&(u=i-n),l>u&&(u=l),a={},a.top=u,a.left=p,t.css(a),$(e.currentTarget.parentNode).find("#api_information_panel").show()},t.prototype.mouseExit=function(e){return $(e.currentTarget.parentNode).find("#api_information_panel").hide()},t.prototype.render=function(){var e,t,n,a,s,i,r,l,o,p,u,h,c,d,m,f,y,g,v,_,w,b,x,k,O,C,S,P,T,D,H,M,E,R,V,N,U,A;if(i=jQuery.inArray(this.model.method,this.model.supportedSubmitMethods())>=0,i||(this.model.isReadOnly=!0),this.model.description=this.model.description||this.model.notes,this.model.description&&(this.model.description=this.model.description.replace(/(?:\r\n|\r|\n)/g,"<br />")),this.model.oauth=null,o=this.model.authorizations||this.model.security)if(Array.isArray(o))for(k=0,P=o.length;P>k;k++){n=o[k];for(l in n){t=n[l];for(e in this.auths)if(t=this.auths[e],"oauth2"===t.type){this.model.oauth={},this.model.oauth.scopes=[],R=t.value.scopes;for(r in R)b=R[r],y=n[l].indexOf(r),y>=0&&(p={scope:r,description:b},this.model.oauth.scopes.push(p))}}}else for(r in o)if(b=o[r],"oauth2"===r)for(null===this.model.oauth&&(this.model.oauth={}),void 0===this.model.oauth.scopes&&(this.model.oauth.scopes=[]),O=0,T=b.length;T>O;O++)p=b[O],this.model.oauth.scopes.push(p);if("undefined"!=typeof this.model.responses){this.model.responseMessages=[],V=this.model.responses;for(a in V)x=V[a],m=null,f=this.model.responses[a].schema,f&&f.$ref&&(m=f.$ref,0===m.indexOf("#/definitions/")&&(m=m.substring("#/definitions/".length))),this.model.responseMessages.push({code:a,message:x.description,responseModel:m})}if("undefined"==typeof this.model.responseMessages&&(this.model.responseMessages=[]),g=null,this.model.successResponse){_=this.model.successResponse;for(l in _)x=_[l],this.model.successCode=l,"object"==typeof x&&"function"==typeof x.createJSONSample&&(g={sampleJSON:JSON.stringify(x.createJSONSample(),void 0,2),isParam:!1,signature:x.getMockSignature()})}else this.model.responseClassSignature&&"string"!==this.model.responseClassSignature&&(g={sampleJSON:this.model.responseSampleJSON,isParam:!1,signature:this.model.responseClassSignature});for($(this.el).html(Handlebars.templates.operation(this.model)),g?(d=new SignatureView({model:g,tagName:"div"}),$(".model-signature",$(this.el)).append(d.render().el)):(this.model.responseClassSignature="string",$(".model-signature",$(this.el)).html(this.model.type)),s={isParam:!1},s.consumes=this.model.consumes,s.produces=this.model.produces,N=this.model.parameters,C=0,D=N.length;D>C;C++)u=N[C],w=u.type||u.dataType||"","undefined"==typeof w&&(m=u.schema,m&&m.$ref&&(h=m.$ref,w=0===h.indexOf("#/definitions/")?h.substring("#/definitions/".length):h)),w&&"file"===w.toLowerCase()&&(s.consumes||(s.consumes="multipart/form-data")),u.type=w;for(c=new ResponseContentTypeView({model:s}),$(".response-content-type",$(this.el)).append(c.render().el),U=this.model.parameters,S=0,H=U.length;H>S;S++)u=U[S],this.addParameter(u,s.consumes);for(A=this.model.responseMessages,E=0,M=A.length;M>E;E++)v=A[E],this.addStatusCode(v);return this},t.prototype.addParameter=function(e,t){var n;return e.consumes=t,n=new ParameterView({model:e,tagName:"tr",readOnly:this.model.isReadOnly}),$(".operation-params",$(this.el)).append(n.render().el)},t.prototype.addStatusCode=function(e){var t;return t=new StatusCodeView({model:e,tagName:"tr"}),$(".operation-status",$(this.el)).append(t.render().el)},t.prototype.submitOperation=function(e){var t,n,a,s,i,r,l,o,p,u,h,c,d,m,f,y;if(null!=e&&e.preventDefault(),n=$(".sandbox",$(this.el)),t=!0,n.find("input.required").each(function(){return $(this).removeClass("error"),""===jQuery.trim($(this).val())?($(this).addClass("error"),$(this).wiggle({callback:function(e){return function(){return $(e).focus()}}(this)}),t=!1):void 0}),n.find("textarea.required").each(function(){return $(this).removeClass("error"),""===jQuery.trim($(this).val())?($(this).addClass("error"),$(this).wiggle({callback:function(e){return function(){return $(e).focus()}}(this)}),t=!1):void 0}),t){for(s={},r={parent:this},a=!1,m=n.find("input"),o=0,h=m.length;h>o;o++)i=m[o],null!=i.value&&jQuery.trim(i.value).length>0&&(s[i.name]=i.value),"file"===i.type&&(s[i.name]=i.files[0],a=!0);for(f=n.find("textarea"),p=0,c=f.length;c>p;p++)i=f[p],null!=i.value&&jQuery.trim(i.value).length>0&&(s[i.name]=i.value);for(y=n.find("select"),u=0,d=y.length;d>u;u++)i=y[u],l=this.getSelectedValue(i),null!=l&&jQuery.trim(l).length>0&&(s[i.name]=l);return r.responseContentType=$("div select[name=responseContentType]",$(this.el)).val(),r.requestContentType=$("div select[name=parameterContentType]",$(this.el)).val(),$(".response_throbber",$(this.el)).show(),a?this.handleFileUpload(s,n):this.model["do"](s,r,this.showCompleteStatus,this.showErrorStatus,this)}},t.prototype.success=function(e,t){return t.showCompleteStatus(e)},t.prototype.handleFileUpload=function(e,t){var n,a,s,i,r,l,o,p,u,h,c,d,m,f,y,g,v,_,w;for(g=t.serializeArray(),p=0,d=g.length;d>p;p++)i=g[p],null!=i.value&&jQuery.trim(i.value).length>0&&(e[i.name]=i.value);for(n=new FormData,o=0,v=this.model.parameters,u=0,m=v.length;m>u;u++)l=v[u],("form"===l.paramType||"formData"===l["in"])&&"file"!==l.type.toLowerCase()&&void 0!==e[l.name]&&n.append(l.name,e[l.name]);for(s={},_=this.model.parameters,h=0,f=_.length;f>h;h++)l=_[h],"header"===l.paramType&&(s[l.name]=e[l.name]);for(w=t.find('input[type~="file"]'),c=0,y=w.length;y>c;c++)a=w[c],"undefined"!=typeof a.files[0]&&(n.append($(a).attr("name"),a.files[0]),o+=1);return this.invocationUrl=this.model.supportHeaderParams()?(s=this.model.getHeaderParams(e),delete s["Content-Type"],this.model.urlify(e,!1)):this.model.urlify(e,!0),$(".request_url",$(this.el)).html("<pre></pre>"),$(".request_url pre",$(this.el)).text(this.invocationUrl),r={type:this.model.method,url:this.invocationUrl,headers:s,data:n,dataType:"json",contentType:!1,processData:!1,error:function(e){return function(t){return e.showErrorStatus(e.wrap(t),e)}}(this),success:function(e){return function(t){return e.showResponse(t,e)}}(this),complete:function(e){return function(t){return e.showCompleteStatus(e.wrap(t),e)}}(this)},window.authorizations&&window.authorizations.apply(r),0===o&&r.data.append("fake","true"),jQuery.ajax(r),!1},t.prototype.wrap=function(e){var t,n,a,s,i,r,l;for(a={},n=e.getAllResponseHeaders().split("\r"),r=0,l=n.length;l>r;r++)s=n[r],t=s.match(/^([^:]*?):(.*)$/),t||(t=[]),t.shift(),void 0!==t[0]&&void 0!==t[1]&&(a[t[0].trim()]=t[1].trim());return i={},i.content={},i.content.data=e.responseText,i.headers=a,i.request={},i.request.url=this.invocationUrl,i.status=e.status,i},t.prototype.getSelectedValue=function(e){var t,n,a,s,i;if(e.multiple){for(n=[],i=e.options,a=0,s=i.length;s>a;a++)t=i[a],t.selected&&n.push(t.value);return n.length>0?n:null}return e.value},t.prototype.hideResponse=function(e){return null!=e&&e.preventDefault(),$(".response",$(this.el)).slideUp(),$(".response_hider",$(this.el)).fadeOut()},t.prototype.showResponse=function(e){var t;return t=JSON.stringify(e,null,"	").replace(/\n/g,"<br>"),$(".response_body",$(this.el)).html(escape(t))},t.prototype.showErrorStatus=function(e,t){return t.showStatus(e)},t.prototype.showCompleteStatus=function(e,t){return t.showStatus(e)},t.prototype.formatXml=function(e){var t,n,a,s,i,r,l,o,p,u,h,c,d;for(o=/(>)(<)(\/*)/g,u=/[ ]*(.*)[ ]+\n/g,t=/(<.+>)(.+\n)/g,e=e.replace(o,"$1\n$2$3").replace(u,"$1\n").replace(t,"$1\n$2"),l=0,n="",i=e.split("\n"),a=0,s="other",p={"single->single":0,"single->closing":-1,"single->opening":0,"single->other":0,"closing->single":0,"closing->closing":-1,"closing->opening":0,"closing->other":0,"opening->single":1,"opening->closing":0,"opening->opening":1,"opening->other":1,"other->single":0,"other->closing":-1,"other->opening":0,"other->other":0},h=function(e){var t,i,r,l,o,u,h;return u={single:Boolean(e.match(/<.+\/>/)),closing:Boolean(e.match(/<\/.+>/)),opening:Boolean(e.match(/<[^!?].*>/))},o=function(){var e;e=[];for(r in u)h=u[r],h&&e.push(r);return e}()[0],o=void 0===o?"other":o,t=s+"->"+o,s=o,l="",a+=p[t],l=function(){var e,t,n;for(n=[],i=e=0,t=a;t>=0?t>e:e>t;i=t>=0?++e:--e)n.push("  ");return n}().join(""),"opening->closing"===t?n=n.substr(0,n.length-1)+e+"\n":n+=l+e+"\n"},c=0,d=i.length;d>c;c++)r=i[c],h(r);return n},t.prototype.showStatus=function(e){var t,n,a,s,i,r,l,o,p,u,h;if(void 0===e.content?(n=e.data,h=e.url):(n=e.content.data,h=e.request.url),i=e.headers,a=null,i&&(a=i["Content-Type"]||i["content-type"],a&&(a=a.split(";")[0].trim())),$(".response_body",$(this.el)).removeClass("json"),$(".response_body",$(this.el)).removeClass("xml"),n)if("application/json"===a||/\+json$/.test(a)){r=null;try{r=JSON.stringify(JSON.parse(n),null,"  ")}catch(c){s=c,r="can't parse JSON.  Raw result:\n\n"+n}t=$("<code />").text(r),o=$('<pre class="json" />').append(t)}else"application/xml"===a||/\+xml$/.test(a)?(t=$("<code />").text(this.formatXml(n)),o=$('<pre class="xml" />').append(t)):"text/html"===a?(t=$("<code />").html(_.escape(n)),o=$('<pre class="xml" />').append(t)):/^image\//.test(a)?o=$("<img>").attr("src",h):(t=$("<code />").text(n),o=$('<pre class="json" />').append(t));else t=$("<code />").text("no content"),o=$('<pre class="json" />').append(t);return p=o,$(".request_url",$(this.el)).html("<pre></pre>"),$(".request_url pre",$(this.el)).text(h),$(".response_code",$(this.el)).html("<pre>"+e.status+"</pre>"),$(".response_body",$(this.el)).html(p),$(".response_headers",$(this.el)).html("<pre>"+_.escape(JSON.stringify(e.headers,null,"  ")).replace(/\n/g,"<br>")+"</pre>"),$(".response",$(this.el)).slideDown(),$(".response_hider",$(this.el)).show(),$(".response_throbber",$(this.el)).hide(),u=$(".response_body",$(this.el))[0],l=this.options.swaggerOptions,l.highlightSizeThreshold&&e.data.length>l.highlightSizeThreshold?u:hljs.highlightBlock(u)},t.prototype.toggleOperationContent=function(){var e;return e=$("#"+Docs.escapeResourceName(this.parentId+"_"+this.nickname+"_content")),e.is(":visible")?Docs.collapseOperation(e):Docs.expandOperation(e)},t}(Backbone.View);var ParameterContentTypeView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;ParameterContentTypeView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(){},t.prototype.render=function(){var e;return e=this.template(),$(this.el).html(e(this.model)),$("label[for=parameterContentType]",$(this.el)).text("Parameter content type:"),this},t.prototype.template=function(){return Handlebars.templates.parameter_content_type},t}(Backbone.View),this.Handlebars.templates.param_required=Handlebars.template({1:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(2,a),inverse:this.program(4,a),data:a}),null!=s&&(i+=s),i},2:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return'			<input type="file" name=\''+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"'/>\n"},4:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(5,a),inverse:this.program(7,a),data:a}),null!=s&&(i+=s),i},5:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<textarea class='body-textarea required' placeholder='(required)' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"'>"+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+'</textarea>\n        <br />\n        <div class="parameter-content-type" />\n'},7:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<textarea class='body-textarea required' placeholder='(required)' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+'\'></textarea>\n				<br />\n				<div class="parameter-content-type" />\n'},9:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e.isFile:e,{name:"if",hash:{},fn:this.program(10,a),inverse:this.program(12,a),data:a}),null!=s&&(i+=s),i},10:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"			<input class='parameter' class='required' type='file' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"'/>\n"},12:function(e,t,n,a){var s,i="";return s=t["if"].call(e,null!=e?e["default"]:e,{name:"if",hash:{},fn:this.program(13,a),inverse:this.program(15,a),data:a}),null!=s&&(i+=s),i},13:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<input class='parameter required' minlength='1' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"' placeholder='(required)' type='text' value='"+l((s=null!=(s=t["default"]||(null!=e?e["default"]:e))?s:r,typeof s===i?s.call(e,{name:"default",hash:{},data:a}):s))+"'/>\n"},15:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"				<input class='parameter required' minlength='1' name='"+l((s=null!=(s=t.name||(null!=e?e.name:e))?s:r,typeof s===i?s.call(e,{name:"name",hash:{},data:a}):s))+"' placeholder='(required)' type='text' value=''/>\n"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p="<td class='code required'>"+o((i=null!=(i=t.name||(null!=e?e.name:e))?i:l,typeof i===r?i.call(e,{name:"name",hash:{},data:a}):i))+"</td>\n<td>\n";return s=t["if"].call(e,null!=e?e.isBody:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.program(9,a),data:a}),null!=s&&(p+=s),p+='</td>\n<td>\n	<strong><span class="markdown">',i=null!=(i=t.description||(null!=e?e.description:e))?i:l,s=typeof i===r?i.call(e,{name:"description",hash:{},data:a}):i,null!=s&&(p+=s),p+="</span></strong>\n</td>\n<td>",i=null!=(i=t.paramType||(null!=e?e.paramType:e))?i:l,s=typeof i===r?i.call(e,{name:"paramType",hash:{},data:a}):i,null!=s&&(p+=s),p+'</td>\n<td><span class="model-signature"></span></td>\n'},useData:!0}),this.Handlebars.templates.parameter_content_type=Handlebars.template({1:function(e,t,n,a){var s,i="";return s=t.each.call(e,null!=e?e.consumes:e,{name:"each",hash:{},fn:this.program(2,a),inverse:this.noop,data:a}),null!=s&&(i+=s),i},2:function(e){var t,n=this.lambda,a='  <option value="';return t=n(e,e),null!=t&&(a+=t),a+='">',t=n(e,e),null!=t&&(a+=t),a+"</option>\n"},4:function(){return'  <option value="application/json">application/json</option>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i='<label for="parameterContentType"></label>\n<select name="parameterContentType">\n';return s=t["if"].call(e,null!=e?e.consumes:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.program(4,a),data:a}),null!=s&&(i+=s),i+"</select>\n"},useData:!0});var ParameterView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;ParameterView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(){return Handlebars.registerHelper("isArray",function(e,t){return"array"===e.type.toLowerCase()||e.allowMultiple?t.fn(this):t.inverse(this)})},t.prototype.render=function(){var e,t,n,a,s,i,r,l,o,p;return p=this.model.type||this.model.dataType,"undefined"==typeof p&&(i=this.model.schema,i&&i.$ref&&(a=i.$ref,p=0===a.indexOf("#/definitions/")?a.substring("#/definitions/".length):a)),this.model.type=p,this.model.paramType=this.model["in"]||this.model.paramType,("body"===this.model.paramType||"body"===this.model["in"])&&(this.model.isBody=!0),p&&"file"===p.toLowerCase()&&(this.model.isFile=!0),this.model["default"]=this.model["default"]||this.model.defaultValue,this.model.allowableValues&&(this.model.isList=!0),o=this.template(),$(this.el).html(o(this.model)),r={sampleJSON:this.model.sampleJSON,isParam:!0,signature:this.model.signature},this.model.sampleJSON?(l=new SignatureView({model:r,tagName:"div"}),$(".model-signature",$(this.el)).append(l.render().el)):$(".model-signature",$(this.el)).html(this.model.signature),t=!1,this.model.isBody&&(t=!0),e={isParam:t},e.consumes=this.model.consumes,t?(n=new ParameterContentTypeView({model:e}),$(".parameter-content-type",$(this.el)).append(n.render().el)):(s=new ResponseContentTypeView({model:e}),$(".response-content-type",$(this.el)).append(s.render().el)),this},t.prototype.template=function(){return this.model.isList?Handlebars.templates.param_list:this.options.readOnly?this.model.required?Handlebars.templates.param_readonly_required:Handlebars.templates.param_readonly:this.model.required?Handlebars.templates.param_required:Handlebars.templates.param},t}(Backbone.View);var ResourceView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;ResourceView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(e){return null==e&&(e={}),this.auths=e.auths,""===this.model.description&&(this.model.description=null),null!=this.model.description?this.model.summary=this.model.description:void 0},t.prototype.render=function(){var e,t,n,a,s,i,r;for(n={},$(this.el).html(Handlebars.templates.resource(this.model)),r=this.model.operationsArray,s=0,i=r.length;i>s;s++){for(a=r[s],e=0,t=a.nickname;"undefined"!=typeof n[t];)t=t+"_"+e,e+=1;n[t]=a,a.nickname=t,a.parentId=this.model.id,this.addOperation(a)}return $(".toggleEndpointList",this.el).click(this.callDocs.bind(this,"toggleEndpointListForResource")),$(".collapseResource",this.el).click(this.callDocs.bind(this,"collapseOperationsForResource")),$(".expandResource",this.el).click(this.callDocs.bind(this,"expandOperationsForResource")),this},t.prototype.addOperation=function(e){var t;return e.number=this.number,t=new OperationView({model:e,tagName:"li",className:"endpoint",swaggerOptions:this.options.swaggerOptions,auths:this.auths}),$(".endpoints",$(this.el)).append(t.render().el),this.number++},t.prototype.callDocs=function(e,t){return t.preventDefault(),Docs[e](t.currentTarget.getAttribute("data-id"))},t}(Backbone.View),this.Handlebars.templates.resource=Handlebars.template({1:function(){return" : "},3:function(e,t,n,a){var s,i="function",r=t.helperMissing,l=this.escapeExpression;return"<li>\n      <a href='"+l((s=null!=(s=t.url||(null!=e?e.url:e))?s:r,typeof s===i?s.call(e,{name:"url",hash:{},data:a}):s))+"'>Raw</a>\n    </li>"},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r,l="function",o=t.helperMissing,p=this.escapeExpression,u=t.blockHelperMissing,h="<div class='heading'>\n  <h2>\n    <a href='#!/"+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+'\' class="toggleEndpointList" data-id="'+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+'">'+p((i=null!=(i=t.name||(null!=e?e.name:e))?i:o,typeof i===l?i.call(e,{name:"name",hash:{},data:a}):i))+"</a> ";return i=null!=(i=t.summary||(null!=e?e.summary:e))?i:o,r={name:"summary",hash:{},fn:this.program(1,a),inverse:this.noop,data:a},s=typeof i===l?i.call(e,r):i,t.summary||(s=u.call(e,s,r)),null!=s&&(h+=s),i=null!=(i=t.summary||(null!=e?e.summary:e))?i:o,s=typeof i===l?i.call(e,{name:"summary",hash:{},data:a}):i,null!=s&&(h+=s),h+="\n  </h2>\n  <ul class='options'>\n    <li>\n      <a href='#!/"+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+"' id='endpointListTogger_"+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+'\' class="toggleEndpointList" data-id="'+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+'">Show/Hide</a>\n    </li>\n    <li>\n      <a href=\'#\' class="collapseResource" data-id="'+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+'">\n        List Operations\n      </a>\n    </li>\n    <li>\n      <a href=\'#\' class="expandResource" data-id="'+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+'">\n        Expand Operations\n      </a>\n    </li>\n    ',i=null!=(i=t.url||(null!=e?e.url:e))?i:o,r={name:"url",hash:{},fn:this.program(3,a),inverse:this.noop,data:a},s=typeof i===l?i.call(e,r):i,t.url||(s=u.call(e,s,r)),null!=s&&(h+=s),h+"\n  </ul>\n</div>\n<ul class='endpoints' id='"+p((i=null!=(i=t.id||(null!=e?e.id:e))?i:o,typeof i===l?i.call(e,{name:"id",hash:{},data:a}):i))+"_endpoint_list' style='display:none'>\n\n</ul>\n"},useData:!0});var ResponseContentTypeView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;ResponseContentTypeView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(){},t.prototype.render=function(){var e;return e=this.template(),$(this.el).html(e(this.model)),$("label[for=responseContentType]",$(this.el)).text("Response Content Type"),this},t.prototype.template=function(){return Handlebars.templates.response_content_type},t}(Backbone.View),this.Handlebars.templates.response_content_type=Handlebars.template({1:function(e,t,n,a){var s,i="";return s=t.each.call(e,null!=e?e.produces:e,{name:"each",hash:{},fn:this.program(2,a),inverse:this.noop,data:a}),null!=s&&(i+=s),i},2:function(e){var t,n=this.lambda,a='  <option value="';return t=n(e,e),null!=t&&(a+=t),a+='">',t=n(e,e),null!=t&&(a+=t),a+"</option>\n"},4:function(){return'  <option value="application/json">application/json</option>\n'},compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i='<label for="responseContentType"></label>\n<select name="responseContentType">\n';return s=t["if"].call(e,null!=e?e.produces:e,{name:"if",hash:{},fn:this.program(1,a),inverse:this.program(4,a),data:a}),null!=s&&(i+=s),i+"</select>\n"},useData:!0});var SignatureView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;SignatureView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.events={"click a.description-link":"switchToDescription","click a.snippet-link":"switchToSnippet","mousedown .snippet":"snippetToTextArea"},t.prototype.initialize=function(){},t.prototype.render=function(){var e;return e=this.template(),$(this.el).html(e(this.model)),this.switchToSnippet(),this.isParam=this.model.isParam,this.isParam&&$(".notice",$(this.el)).text("Click to set as parameter value"),this},t.prototype.template=function(){return Handlebars.templates.signature},t.prototype.switchToDescription=function(e){return null!=e&&e.preventDefault(),$(".snippet",$(this.el)).hide(),$(".description",$(this.el)).show(),$(".description-link",$(this.el)).addClass("selected"),$(".snippet-link",$(this.el)).removeClass("selected")},t.prototype.switchToSnippet=function(e){return null!=e&&e.preventDefault(),$(".description",$(this.el)).hide(),$(".snippet",$(this.el)).show(),$(".snippet-link",$(this.el)).addClass("selected"),$(".description-link",$(this.el)).removeClass("selected")},t.prototype.snippetToTextArea=function(e){var t;return this.isParam&&(null!=e&&e.preventDefault(),t=$("textarea",$(this.el.parentNode.parentNode.parentNode)),""===$.trim(t.val()))?t.val(this.model.sampleJSON):void 0},t}(Backbone.View),this.Handlebars.templates.signature=Handlebars.template({compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p='<div>\n<ul class="signature-nav">\n  <li><a class="description-link" href="#">Model</a></li>\n  <li><a class="snippet-link" href="#">Model Schema</a></li>\n</ul>\n<div>\n\n<div class="signature-container">\n  <div class="description">\n    ';return i=null!=(i=t.signature||(null!=e?e.signature:e))?i:l,s=typeof i===r?i.call(e,{name:"signature",hash:{},data:a}):i,null!=s&&(p+=s),p+'\n  </div>\n\n  <div class="snippet">\n    <pre><code>'+o((i=null!=(i=t.sampleJSON||(null!=e?e.sampleJSON:e))?i:l,typeof i===r?i.call(e,{name:"sampleJSON",hash:{},data:a}):i))+'</code></pre>\n    <small class="notice"></small>\n  </div>\n</div>\n\n'},useData:!0});var StatusCodeView,__extends=function(e,t){function n(){this.constructor=e}for(var a in t)__hasProp.call(t,a)&&(e[a]=t[a]);return n.prototype=t.prototype,e.prototype=new n,e.__super__=t.prototype,e},__hasProp={}.hasOwnProperty;StatusCodeView=function(e){function t(){return t.__super__.constructor.apply(this,arguments)}return __extends(t,e),t.prototype.initialize=function(){},t.prototype.render=function(){var e,t,n;return n=this.template(),$(this.el).html(n(this.model)),swaggerUi.api.models.hasOwnProperty(this.model.responseModel)?(e={sampleJSON:JSON.stringify(swaggerUi.api.models[this.model.responseModel].createJSONSample(),null,2),isParam:!1,signature:swaggerUi.api.models[this.model.responseModel].getMockSignature()},t=new SignatureView({model:e,tagName:"div"}),$(".model-signature",this.$el).append(t.render().el)):$(".model-signature",this.$el).html(""),this},t.prototype.template=function(){return Handlebars.templates.status_code},t}(Backbone.View),this.Handlebars.templates.status_code=Handlebars.template({compiler:[6,">= 2.0.0-beta.1"],main:function(e,t,n,a){var s,i,r="function",l=t.helperMissing,o=this.escapeExpression,p="<td width='15%' class='code'>"+o((i=null!=(i=t.code||(null!=e?e.code:e))?i:l,typeof i===r?i.call(e,{name:"code",hash:{},data:a}):i))+"</td>\n<td>";return i=null!=(i=t.message||(null!=e?e.message:e))?i:l,s=typeof i===r?i.call(e,{name:"message",hash:{},data:a}):i,null!=s&&(p+=s),p+"</td>\n<td width='50%'><span class=\"model-signature\" /></td>"},useData:!0});