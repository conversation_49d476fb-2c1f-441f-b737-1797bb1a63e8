﻿using System;
using System.Collections.Generic;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    public class PagingDataSources<T> where T : class
    {
        public int Total { get; set; }
        public int PageSize { get; set; }
        public IList<T> Data { get; set; }
        public DateTime Timestamp => DateTime.Now;
    }
    public class SyncDataSources<T> : PagingDataSources<T> where T : class
    {
        public IList<long> RemovedIds { get; set; }
    }
}