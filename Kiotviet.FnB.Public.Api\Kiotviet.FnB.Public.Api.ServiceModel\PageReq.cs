﻿using System;
using System.Collections.Specialized;
using ServiceStack;
using System.Runtime.Serialization;
namespace KiotViet.FnB.Public.Api.ServiceModel
{
    public class PageReq
    {
        [ApiMember(Name = "orderBy", Description = "Sắp xếp dữ liệu theo trường orderBy (ví dụ: orderBy=Name)",
        ParameterType = "Query", DataType = "String", IsRequired = false)]
        [DataMember(Name = "orderBy")]
        public string Orderby { get; set; }

        [ApiMember(Name = "orderDirection", Description = "Sắp xếp kết quả trả về theo: Tăng dần ASC (Mặc định), giảm dần DESC", DataType = "Query", IsRequired = false)]
        [DataMember(Name = "orderDirection")]
        public string OrderDirection { get; set; }

        [ApiMember(Name = "currentItem",Description = "Lấy dữ liệu từ bản ghi currentItem", ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "currentItem")]
        public int? CurrentItem { get; set; }

        [ApiMember(Name = "pageSize", Description = "Số item trong một trang, mặc định 20 items, tối đa 100 items", ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "pageSize")]
        public int? PageSize { get; set; }

        [ApiMember(Name = "lastModifiedFrom", Description = "Thời gian thay đổi dữ liệu lần cuối (nhằm hỗ trợ đồng bộ dữ liệu dựa theo TimeStamp)", ParameterType = "Query", DataType = "DateTime", IsRequired = false)]
        [DataMember(Name = "lastModifiedFrom")]
        public DateTime? LastModifiedFrom { get; set; }

        public NameValueCollection GetModelFilter()
        {
            var retVal = new NameValueCollection();
            if (string.IsNullOrEmpty(Orderby)) return retVal;
            var orderDirection = string.IsNullOrEmpty(OrderDirection) ? "asc" : OrderDirection.ToLower();
            var orderby = $"{Orderby} {orderDirection}";
            retVal.Add("$orderby", orderby);
            return retVal;
        }
    }
}