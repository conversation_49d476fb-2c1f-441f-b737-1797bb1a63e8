﻿using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace KiotViet.FnB.Public.Api.ServiceModel
{
    [Route("/surchages", "GET", Summary = "API trả lại toàn bộ danh sách thu khác của cửa hàng đã được xác nhận", Notes = "API trả lại toàn bộ danh sách thu khác của cửa hàng đã được xác nhận")]
    public class SurchagesList : PageReq, IReturn<SyncDataSources<SurchagesDTO>>
    {
        [ApiMember(Name = "branchId", Description = "Id chi nhánh", ParameterType = "Query", DataType = "int", IsRequired = false)]
        [DataMember(Name = "branchId")]
        public int? BranchId { get; set; }

        [DataMember(Name = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }
    }
    [Route("/surchages", "POST")]
    public class SurchargeCreate : SurchargeRequest, IReturn<object>
    {
    }
    [Route("/surchages/{id}", "PUT")]
    public class SurchargeUpdate : SurchargeRequest, IReturn<object>
    {
        [DataMember(Name = "id")]
        public long Id { get; set; }
    }
    [Route("/surchages/{id}/activesurchage", "POST")]
    public class ActiveSurcharge : IReturn<object>
    {
        public long Id { get; set; }
        public bool IsActive { get; set; }
    }
    [DataContract]
    public class SurchagesDTO
    {
        [DataMember(Name = "id")]
        public long Id { get; set; }
        [DataMember(Name = "surchargeCode")]
        public string Code { get; set; }
        [DataMember(Name = "surchargeName")]
        public string Name { get; set; }
        [DataMember(Name = "retailerId")]
        public int RetailerId { get; set; }
        [DataMember(Name = "valueRatio")]
        public double? ValueRatio { get; set; }
        [DataMember(Name = "value")]
        public Decimal? Value { get; set; }
        [DataMember(Name = "createdDate")]
        public DateTime CreatedDate { get; set; }
        [DataMember(Name = "modifiedDate")]
        public DateTime? ModifiedDate { get; set; }
        [DataMember(Name = "isActive")]
        public bool isActive { get; set; }
    }

    public class SurchargeRequest
    {
        [DataMember(Name = "name")]
        public string Name { get; set; }
        [DataMember(Name = "code")]
        public string Code { get; set; }
        [DataMember(Name = "value")]
        public decimal? Value { get; set; }
    }
}
