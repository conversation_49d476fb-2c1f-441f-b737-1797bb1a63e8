﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{582DFB0F-F84D-4E43-9BCA-2A5A86600B7A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>KiotViet.FnB.Public.Api.ServiceInterface</RootNamespace>
    <AssemblyName>KiotViet.FnB.Public.Api.ServiceInterface</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <RestorePackages>true</RestorePackages>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=4.6.1.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.4.6.1\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Auth">
      <HintPath>..\..\Component\KiotViet.Auth.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.CommandDispatcher">
      <HintPath>..\..\Component\KiotViet.CommandDispatcher.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Exceptions">
      <HintPath>..\..\Component\KiotViet.Exceptions.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.FirebaseRealTimeDatabase">
      <HintPath>..\..\Component\KiotViet.FirebaseRealTimeDatabase.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.MongoDb">
      <HintPath>..\..\Component\KiotViet.MongoDb.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.MongoServices">
      <HintPath>..\..\Component\KiotViet.MongoServices.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Persistence">
      <HintPath>..\..\Component\KiotViet.Persistence.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.RedisServices">
      <HintPath>..\..\Component\KiotViet.RedisServices.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Resources">
      <HintPath>..\..\Component\KiotViet.Resources.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Services">
      <HintPath>..\..\Component\KiotViet.Services.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Utilities">
      <HintPath>..\..\Component\KiotViet.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Web.Api">
      <HintPath>..\..\Component\KiotViet.Web.Api.dll</HintPath>
    </Reference>
    <Reference Include="Linq2Rest">
      <HintPath>..\..\Component\Linq2Rest.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit, Version=1.1.2.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\..\packages\LINQKit.1.1.2\lib\net45\LinqKit.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=2.2.4.26, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MongoDB.Bson.2.2.4\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=2.2.4.26, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MongoDB.Driver.2.2.4\lib\net45\MongoDB.Driver.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Core, Version=2.2.4.26, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MongoDB.Driver.Core.2.2.4\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Legacy, Version=2.2.4.26, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\mongocsharpdriver.2.2.4\lib\net45\MongoDB.Driver.Legacy.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.5.4.0\lib\net45\ServiceStack.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Api.Swagger, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Api.Swagger.5.4.0\lib\net45\ServiceStack.Api.Swagger.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Client, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Client.5.4.0\lib\net45\ServiceStack.Client.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Common, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Common.5.4.0\lib\net45\ServiceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Interfaces.5.4.0\lib\net45\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Logging.Log4Net, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Logging.Log4Net.5.4.0\lib\net45\ServiceStack.Logging.Log4Net.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.OrmLite, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.OrmLite.5.4.0\lib\net45\ServiceStack.OrmLite.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Razor, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Razor.5.4.0\lib\net45\ServiceStack.Razor.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Redis, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Redis.5.4.0\lib\net45\ServiceStack.Redis.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Server, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Server.5.4.0\lib\net45\ServiceStack.Server.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Text, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Text.5.4.0\lib\net45\ServiceStack.Text.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Buffers.4.4.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Memory, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Memory.4.5.1\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Core" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Runtime.CompilerServices.Unsafe.4.5.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Razor.3.2.6\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaseApi.cs" />
    <Compile Include="BranchApi.cs" />
    <Compile Include="CategoryApi.cs" />
    <Compile Include="Common\KVJwtAuthProviderReader.cs" />
    <Compile Include="Common\WebHookHelper.cs" />
    <Compile Include="CustomerApi.cs" />
    <Compile Include="CustomerGroupApi.cs" />
    <Compile Include="Filters\CheckNewFnbFilterAttribute.cs" />
    <Compile Include="Filters\RateLimitFilterAttribute.cs" />
    <Compile Include="InvoiceApi.cs" />
    <Compile Include="WebhookApi.cs" />
    <Compile Include="PingApi.cs" />
    <Compile Include="OrderApi.cs" />
    <Compile Include="ProductApi.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UserApi.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Kiotviet.FnB.Public.Api.ServiceModel\KiotViet.FnB.Public.Api.ServiceModel.csproj">
      <Project>{08bef328-ab7d-48d3-800d-48acc75c7914}</Project>
      <Name>KiotViet.FnB.Public.Api.ServiceModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\KiotViet.FnB.Public.Api.WebhookService\KiotViet.FnB.Public.Api.WebhookHelper.csproj">
      <Project>{91314bd2-08dd-48e1-8539-79c69a491caf}</Project>
      <Name>KiotViet.FnB.Public.Api.WebhookHelper</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Import Project="..\..\Component\BuildCommon.targets" Condition="Exists('..\..\Component\BuildCommon.targets')" />
  <Import Project="..\..\packages\MSBuildTasks.1.5.0.235\build\MSBuildTasks.targets" Condition="Exists('..\..\packages\MSBuildTasks.1.5.0.235\build\MSBuildTasks.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\MSBuildTasks.1.5.0.235\build\MSBuildTasks.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\MSBuildTasks.1.5.0.235\build\MSBuildTasks.targets'))" />
  </Target>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)|$(SignAssembly)'=='Release|AnyCPU|True'">
    <PostBuildEvent>kvfnb-sign $(TargetPath)</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>