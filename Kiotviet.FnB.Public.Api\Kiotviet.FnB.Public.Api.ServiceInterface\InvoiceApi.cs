﻿using KiotViet.Exceptions;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Persistence;
using KiotViet.Persistence.Common;
using KiotViet.Resources;
using KiotViet.Services.Interface;
using KiotViet.Utilities;
using Linq2Rest;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class InvoiceApi : BaseApi
    {
        public IInvoiceService InvoiceService { get; set; }
        public IInvoiceDetailService InvoiceDetailService { get; set; }
        public IDeliveryInfoService DeliveryInfoService { get; set; }
        public IInvoiceDeliveryService InvoiceDeliveryService { get; set; }
        public IInvoiceOrderSurchargeService InvoiceOrderSurchargeService { get; set; }
        public IPaymentService PaymentService { get; set; }
        public IReturnService ReturnService { get; set; }
        public IReturnDetailService ReturnDetailService { get; set; }
        public ISaleChannelService SaleChannelService { get; set; }
        public ICustomerService CustomerService { get; set; }
        public IUserService UserService { get; set; }
        public IBranchService BranchService { get; set; }
        public IProductService ProductService { get; set; }
        public IOrderService OrderService { get; set; }
        public async Task<object> Get(GetInvoiceList req)
        {
            var branches = (await BranchService.GetAll().Where(p => p.IsActive).Select(b => new { b.Id, b.Name }).ToListAsync())
                .Select(b => new Branch { Id = b.Id, Name = b.Name }).ToList();

            if (req.BranchIds != null && req.BranchIds.Length > 0)
            {
                branches = branches.Where(b => req.BranchIds.Contains(b.Id)).ToList();
            }

            #region optimization for Index db
            if ((req.FromPurchaseDate == null || req.FromPurchaseDate == DateTime.MinValue) &&
                (req.CreatedDate == null || req.CreatedDate == DateTime.MinValue) &&
                (req.LastModifiedFrom == null || req.LastModifiedFrom == DateTime.MinValue))
            {
                req.FromPurchaseDate = DateTime.Now.AddDays(-ServiceModel.AppConfigInfo.LimitPurchaseDateByDays);
            }


            var limitPurchaseDateByDays = ServiceModel.AppConfigInfo.LimitPurchaseDateByDays;
            if (req.FromPurchaseDate == null && req.CreatedDate.HasValue)
                req.FromPurchaseDate = req.CreatedDate.Value.AddDays(-limitPurchaseDateByDays);

            if (req.FromPurchaseDate == null && req.LastModifiedFrom.HasValue)
                req.FromPurchaseDate = req.LastModifiedFrom.Value.AddDays(-limitPurchaseDateByDays);

            #endregion
            IQueryable<Invoice> invoices = null;
            IQueryable<Invoice> invoiceCount = null;
            if (branches.Count <= ServiceModel.AppConfigInfo.LimmitNumberBranchesQuerryOptimize && branches.Count > 1)
            {
                foreach (var branch in branches)
                {
                    var invoicesTemp = await GetQueryInvoices(req, new List<Branch> { branch }, false);
                    if (invoices == null)
                    {
                        invoices = invoicesTemp;
                    }
                    else
                    {
                        invoices = invoices.Concat(invoicesTemp);
                    }

                    var invoiceCountTemp = await GetQueryInvoices(req, new List<Branch> { branch }, true);
                    if (invoiceCount == null)
                    {
                        invoiceCount = invoiceCountTemp;
                    }
                    else
                    {
                        invoiceCount = invoiceCount.Concat(invoiceCountTemp);
                    }
                }
            } else
            {
                invoices = await GetQueryInvoices(req, branches, false);
                invoiceCount = await GetQueryInvoices(req, branches, true);
            }

            var subOrder = string.Empty;
            if (req.Orderby == null)
            {
                invoices = invoices.OrderByDescending(p => p.PurchaseDate);
                subOrder = nameof(Invoice.PurchaseDate);

            }

            req.Orderby = req.Orderby == null ? subOrder : ConvertOrderName(req.Orderby);

            var lstinvoice = invoices.Filter(req.GetModelFilter()).Cast<Invoice>();
            lstinvoice = lstinvoice.Take(req);
            var invoiceIds = await lstinvoice.Cast<Invoice>().Select(i => i.Id).ToListAsync();
            var invoiceResults = await InvoiceService.GetAll().Where(i => invoiceIds.Contains(i.Id)).Filter(req.GetModelFilter()).Cast<Invoice>().ToListAsync();
            var invoiceList = InvoiceService.DetachByClone(invoiceResults);
            var invoiceData = invoiceList.Select(x => new ServiceModel.InvoiceDTO(x)).ToList();
            var totalInvoice = await invoiceCount.CountAsync();
            await ProcessInvoiceData(req, invoiceData, branches);

            var result = new SyncDataSources<ServiceModel.InvoiceDTO>
            {
                Data = invoiceData,
                PageSize = GetPageSize(req.PageSize),
                Total = totalInvoice
            };

            return await Request.ToOptimizedResultAsync(result);
        }

        private async Task<IQueryable<Invoice>> GetQueryInvoices(GetInvoiceList req, List<Branch> branches, bool getCount = false)
        {
            var invoices = InvoiceService.GetAll().AsNoTracking();

            invoices = FilterInvoiceByPurchaseDate(req, invoices);
            invoices = FilterInvoiceByModifiedDate(req, invoices);
            invoices = FilterInvoiceByStatus(req, invoices);
            invoices = await FilterInvoiceByCustomer(req, invoices);
            invoices = FilterInvoiceByBranch(req, invoices, branches);
            invoices = FilterInvoiceByOrder(req, invoices);
            invoices = FilterInvoiceByCreatedDate(req, invoices);
            if (!getCount && (req.CurrentItem == null || req.CurrentItem == 0) && string.IsNullOrEmpty(req.Orderby))
            {
                invoices = invoices.OrderByDescending(p => p.PurchaseDate);
                invoices = invoices.Take(req);
            }
            
            return invoices;
        }

        public async Task<ServiceModel.InvoiceDTO> Get(GetInvoice req)
        {
            var invoice = await GetInVoiceDetail(req);
            var invoiceDetached = InvoiceService.DetachByClone(invoice, new[] { "Order" });

            UpdateInvoiceDetails(invoice, invoiceDetached);
            UpdateInvoiceSurchanges(invoice, invoiceDetached);
            UpdateOrderpayment(invoice, invoiceDetached);
            await UpdateInvoiceDeliveryInfo(invoice, invoiceDetached);

            var invoiceDto = invoiceDetached.ConvertTo<ServiceModel.InvoiceDTO>();
            invoiceDto.BranchName = invoice.BranchName;
            invoiceDto.CustomerName = invoice.CustomerName;
            invoiceDto.CustomerCode = invoice.CustomerCode;
            invoiceDto.SoldByName = invoice.SoldBy?.GivenName;
            invoiceDto.InvoiceOrderSurcharges = invoice.InvoiceOrderSurcharges.Map(s => s.ConvertTo<InvoiceOrderSurchargeDto>());
            invoiceDto.OrderCode = invoiceDetached.Order?.Code;

            UpdateInvoiceDetails(invoice, invoiceDto);
            UpdateInvoiceDelivery(invoiceDetached, invoiceDto);

            await UpdateInvoiceSaleChannel(invoiceDto);
            await UpdateInvopicePayment(invoice, invoiceDto);
            await UpdateIInvoiceReturn(invoice, invoiceDetached, invoiceDto);

            return invoiceDto;
        }

        private async Task<Dictionary<long?, List<InvoiceOrderSurchargeDto>>> GetInvoiceOrderSurchanges(List<long> invoiceIds)
        {
            return await InvoiceOrderSurchargeService.GetAll()
                .Where(x => x.InvoiceId.HasValue && invoiceIds.Contains(x.InvoiceId.Value))
                .GroupBy(x => x.InvoiceId)
                .ToDictionaryAsync(k => k.Key, v => v.Select(o => new InvoiceOrderSurchargeDto
                {
                    CreatedDate = o.CreatedDate,
                    Id = o.Id,
                    InvoiceId = o.InvoiceId,
                    OrderId = o.OrderId,
                    Price = o.Price,
                    SurchargeCode = o.SurchargeCode,
                    SurchargeId = o.SurchargeId,
                    SurchargeName = o.SurchargeName,
                    SurValue = o.SurValue,
                    SurValueRatio = o.SurValueRatio
                }).ToList());
        }

        private async Task<Dictionary<long, SaleChannelDto>> GetInnvoiceSaleChannels(GetInvoiceList req, List<long> invoiceIds)
        {
            var saleChannels = new Dictionary<long, SaleChannelDto>();
            if (req.IncludeSaleChannel)
            {
                saleChannels = (await SaleChannelService.GetSaleChannelByInvoice(invoiceIds))
                    .ToDictionary(dtos => dtos.InvoiceId.GetValueOrDefault(),
                        dtos => dtos.SaleChannel);
            }

            return saleChannels;
        }

        private async Task<Dictionary<long, InvoiceDeliveryDTO>> GetInvoiceDeliveries(GetInvoiceList req, List<long> invoiceIds)
        {
            var invoiceDeliverys = new Dictionary<long, InvoiceDeliveryDTO>();

            if (!req.IncludeInvoiceDelivery)
            {
                return invoiceDeliverys;
            }

            invoiceDeliverys = (await InvoiceDeliveryService.GetInvoiceDeliveryByInvoices(invoiceIds))
                .ToDictionary(g => g.InvoiceId.GetValueOrDefault(), g => g.Delivery);
            var paymentDeliveries = await PaymentService.GetAll().Where(p => invoiceIds.Contains(p.InvoiceId ?? 0) && p.Code.StartsWith(Payment.CodeCodPrefix)).Select(x => new { InvoiceId = x.InvoiceId, Amount = x.Amount }).ToListAsync();
            foreach (var item in invoiceDeliverys)
            {
                var deliveryPaymets = paymentDeliveries.Where(x => x.InvoiceId == item.Key).ToList();
                if (deliveryPaymets.Any())
                {
                    item.Value.PriceCodPayment = deliveryPaymets.Sum(x => x.Amount);
                }
            }

            return invoiceDeliverys;
        }

        private async Task<Dictionary<long, List<PaymentDTO>>> GetInvoicePayments(GetInvoiceList req, List<long> invoiceIds)
        {
            var payments = new Dictionary<long, List<PaymentDTO>>();
            if (!req.IncludePayment)
            {
                return payments;
            }

            payments = (await PaymentService.GetPaymentByInvoices(invoiceIds)).GroupBy(x => x.InvoiceId)
                .ToDictionary(dtos => dtos.Key.GetValueOrDefault(),
                    dtos => dtos.SelectMany(v => v.Payments).ToList());

            return payments;
        }

        private async Task<Dictionary<long, IEnumerable<ServiceModel.InvoiceDetailDTO>>> GetInvoiceDetailsAsync(List<long> invoiceIds)
        {
            var invoiceDetails = InvoiceDetailService.DetachByClone(InvoiceDetailService.GetAll().Where(x => invoiceIds.Contains(x.InvoiceId)).ToList()).ToList();
            if (invoiceDetails != null && invoiceDetails.Count > 0)
            {
                var productIds = invoiceDetails.Select(id => id.ProductId).Distinct();
                var products = await ProductService.GetAll().Where(p => productIds.Contains(p.Id)).ToListAsync();
                invoiceDetails.ForEach(invoiceDetail => {
                    invoiceDetail.Product = products.FirstOrDefault(p => p.Id == invoiceDetail.ProductId);
                });
            }
          
            var lstInvoiceDetails = invoiceDetails
                .GroupBy(x => x.InvoiceId)
                .ToDictionary(k => k.Key, v => v.Select(x => new ServiceModel.InvoiceDetailDTO
                {
                    Discount = x.ViewDiscount != null && x.ViewDiscount < NumberHelper.KvConstTolerance ? 0 : Math.Round((x.Discount ?? 0) + (((x.Discount ?? 0) < 0 ? -1 : 1) * NumberHelper.KvConstTolerance), NumberHelper.ProductPriceDecPlace),
                    DiscountRatio = x.DiscountRatio,
                    Note = x.Note,
                    Price = Math.Round((x.ViewDiscount != null && x.ViewDiscount < NumberHelper.KvConstTolerance ? x.Price - x.ViewDiscount ?? 0 : x.Price) + NumberHelper.KvConstTolerance, NumberHelper.ProductPriceDecPlace),
                    ProductCode = x.ProductCode,
                    ProductId = x.ProductId,
                    ProductName = x.ProductName,
                    Quantity = x.Quantity,
                    SubTotal = x.SubTotal,
                    UsePoint = x.UsePoint,
                    SerialNumbers = x.SerialNumbers,
                }));
            return lstInvoiceDetails;
        }

        private string ConvertOrderName(string name)
        {
            switch (name.ToLower())
            {
                case "id":
                    return "Id";
                case "orderid":
                    return "OrderId";
                case "code":
                    return "Code";
                case "branchid":
                    return "BranchId";
                case "branchname":
                    return "BranchName";
                case "soldbyname":
                    return "SoldByName";
                case "soldbyid":
                    return "SoldById";
                case "customercode":
                    return "CustomerCode";
                case "customername":
                    return "CustomerName";
                case "total":
                    return "Total";
                case "totalpayment":
                    return "TotalPayment";
                case "discount":
                    return "Discount";
                case "status":
                    return "Status";
                case "statusvalue":
                    return "StatusValue";
                case "salechannelid":
                    return "SaleChannelId";
                case "usingcod":
                    return "UsingCod";
                case "createddate":
                    return "CreatedDate";
                case "purchasedate":
                    return "PurchaseDate";
                case "modifieddate":
                    return "ModifiedDate";
                default:
                    return name;
            }
        }

        private async Task ProcessInvoiceData(GetInvoiceList req, List<ServiceModel.InvoiceDTO> invoiceData, List<Branch> branches)
        {
            var invoiceIds = invoiceData.Select(p => p.Id).ToList();
            var customerIds = invoiceData.Select(i => i.CustomerId).ToList();
            var customers = customerIds.Count > 0 ? await CustomerService.GetAll().Where(c => customerIds.Contains(c.Id)).ToListAsync() : new List<Customer>();

            var soldByIds = invoiceData.Select(i => i.SoldById).ToList();
            var soldByUsers = soldByIds.Count > 0 ? await UserService.GetAll().Where(c => soldByIds.Contains(c.Id)).ToListAsync() : new List<User>();

            var payments = await GetInvoicePayments(req, invoiceIds);
            var invoiceDeliverys = await GetInvoiceDeliveries(req, invoiceIds);
            var saleChannels = await GetInnvoiceSaleChannels(req, invoiceIds);
            var lstInvoiceDetails = await GetInvoiceDetailsAsync(invoiceIds);
            var lstInvoiceOrderSurcharges = await GetInvoiceOrderSurchanges(invoiceIds);

            var orderIds = invoiceData.Select(p => p.OrderId).ToList();
            var orders = await OrderService.GetAll().Where(o => orderIds.Contains(o.Id)).ToListAsync();

            invoiceData.ForEach(invoiceDto =>
            {
                var customer = customers.Where(c => c.Id == invoiceDto.CustomerId).FirstOrDefault();
                if (customer != null)
                {
                    invoiceDto.CustomerName = customer.Name;
                    invoiceDto.CustomerCode = customer.Code;
                }

                var soldBy = soldByUsers.Where(c => c.Id == invoiceDto.SoldById).FirstOrDefault();
                if (soldBy != null)
                {
                    invoiceDto.SoldByName = soldBy.Name;
                }
                MapInvoiceInfo(branches, invoiceDto, payments, invoiceDeliverys, saleChannels, lstInvoiceDetails, lstInvoiceOrderSurcharges, orders);
            });
        }

        private static void MapInvoiceInfo(List<Branch> branches, ServiceModel.InvoiceDTO invoiceDto, Dictionary<long, List<PaymentDTO>> payments, Dictionary<long, InvoiceDeliveryDTO> invoiceDeliverys, Dictionary<long, SaleChannelDto> saleChannels, Dictionary<long, IEnumerable<ServiceModel.InvoiceDetailDTO>> lstInvoiceDetails, Dictionary<long?, List<InvoiceOrderSurchargeDto>> lstInvoiceOrderSurcharges, List<Order> orders)
        {
            var branch = branches.FirstOrDefault(b => b.Id == invoiceDto.BranchId);
            invoiceDto.BranchName = branch != null ? branch.Name : string.Empty;

            var order = orders.FirstOrDefault(b => b.Id == invoiceDto.OrderId);
            invoiceDto.OrderCode = order != null ? order.Code : string.Empty;

            invoiceDto.InvoiceDetails = lstInvoiceDetails.ContainsKey(invoiceDto.Id) ? lstInvoiceDetails[invoiceDto.Id] : null;
            invoiceDto.Payments = payments.ContainsKey(invoiceDto.Id) ? payments[invoiceDto.Id] : null;
            invoiceDto.SaleChannel = saleChannels.ContainsKey(invoiceDto.Id) ? saleChannels[invoiceDto.Id] : null;
            invoiceDto.InvoiceOrderSurcharges = lstInvoiceOrderSurcharges.ContainsKey(invoiceDto.Id) ? lstInvoiceOrderSurcharges[invoiceDto.Id] : null;
            invoiceDto.InvoiceDelivery = invoiceDeliverys.ContainsKey(invoiceDto.Id) ? invoiceDeliverys[invoiceDto.Id] : null;
            if (invoiceDto.InvoiceDelivery != null)
            {
                invoiceDto.InvoiceDelivery.StatusValue = EnumHelper.ToDescription((DeliveryStatus)invoiceDto.InvoiceDelivery.Status);
            }

            if (invoiceDto.Payments != null)
            {
                foreach (var pay in invoiceDto.Payments)
                {
                    if (pay.Status != null) pay.StatusValue = EnumHelper.ToDescription((PaymentStatus)pay.Status);
                }
            }
        }

        private async Task<IQueryable<Invoice>> FilterInvoiceByCustomer(GetInvoiceList req, IQueryable<Invoice> invoices)
        {
            if (req.CustomerIds?.Length > 0)
            {
                invoices = invoices.Where(i => req.CustomerIds.Contains(i.CustomerId));
            }

            if (!string.IsNullOrEmpty(req.CustomerCode))
            {
                var cusIds = await CustomerService.GetAll().Where(p => p.Code.Contains(req.CustomerCode))
                    .Select(t => t.Id).ToListAsync();
                invoices = invoices.Where(i => cusIds.Contains(i.CustomerId ?? 0));
            }

            return invoices;
        }

        private IQueryable<Invoice> FilterInvoiceByCreatedDate(GetInvoiceList req, IQueryable<Invoice> invoices)
        {
            if (req.CreatedDate == null)
            {
                return invoices;
            }

            var nextDate = Convert.ToDateTime(req.CreatedDate).AddDays(1).Date;
            invoices = invoices.Where(i => i.CreatedDate >= req.CreatedDate && i.CreatedDate < nextDate);

            return invoices;
        }

        private IQueryable<Invoice> FilterInvoiceByOrder(GetInvoiceList req, IQueryable<Invoice> invoices)
        {
            if (req.OrderId > 0)
            {
                invoices = invoices.Where(i => i.OrderId == req.OrderId);
            }

            return invoices;
        }

        private IQueryable<Invoice> FilterInvoiceByBranch(GetInvoiceList req, IQueryable<Invoice> invoices, List<Branch> branches)
        {
            var branchIds = branches.Select(p => p.Id).ToArray();

            return invoices.Where(i => branchIds.Contains(i.BranchId));
        }

        private IQueryable<Invoice> FilterInvoiceByStatus(GetInvoiceList req, IQueryable<Invoice> invoices)
        {
            if (req.Status == null || req.Status.Length <= 0)
            {
                req.Status = Enum.GetValues(typeof(InvoiceState)).Cast<int>().ToArray();
            }
            invoices = invoices.Where(i => req.Status.Contains(i.Status));

            return invoices;
        }

        private IQueryable<Invoice> FilterInvoiceByModifiedDate(GetInvoiceList req, IQueryable<Invoice> invoices)
        {
            if (req.LastModifiedFrom != null && req.ToDate != null)
            {
                var i1 = invoices.Where(p => p.ModifiedDate == null && p.PurchaseDate > req.LastModifiedFrom && p.PurchaseDate < req.ToDate);
                var i2 = invoices.Where(p => p.ModifiedDate != null && p.ModifiedDate > req.LastModifiedFrom && p.ModifiedDate < req.ToDate);
                return i1.Concat(i2);
            }

            if (req.LastModifiedFrom != null)
            {
                var i1 = invoices.Where(p => p.ModifiedDate == null && p.PurchaseDate > req.LastModifiedFrom);
                var i2 = invoices.Where(p => p.ModifiedDate != null && p.ModifiedDate > req.LastModifiedFrom);
                invoices = i1.Concat(i2);
            }

            if (req.ToDate != null)
            {
                var i1 = invoices.Where(p => p.ModifiedDate == null && p.PurchaseDate < req.ToDate);
                var i2 = invoices.Where(p => p.ModifiedDate != null && p.ModifiedDate < req.ToDate);
                invoices = i1.Concat(i2);
            }

            return invoices;
        }

        private IQueryable<Invoice> FilterInvoiceByPurchaseDate(GetInvoiceList req, IQueryable<Invoice> invoices)
        {

            if (req.FromPurchaseDate != null)
            {
                invoices = invoices.Where(p => p.PurchaseDate >= req.FromPurchaseDate);
            }
            if (req.ToPurchaseDate != null) {
                var toPurchaseDate = req.ToPurchaseDate.Value.Date.AddDays(1);
                invoices = invoices.Where(p => p.PurchaseDate <= toPurchaseDate);
            }

            return invoices;
        }

        private async Task UpdateInvopicePayment(Invoice invoice, ServiceModel.InvoiceDTO invoiceDto)
        {
            var payments = await PaymentService.GetAll().Where(p => p.InvoiceId == invoice.Id && p.RetailerId == CurrentRetailerId).ToListAsync();
            invoiceDto.Payments = payments.Select(p => new PaymentDTO()
            {
                Id = p.Id,
                AccountId = p.AccountId,
                Amount = p.Amount,
                Code = p.Code,
                Description = p.Description,
                Method = p.Method,
                Status = p.Status,
                TransDate = p.TransDate,
                BankAccount = p.BankAccount?.Account,
                StatusValue = p.Status.HasValue ? EnumHelper.ToDescription((PaymentStatus)p.Status) : ""
            });
        }

        private async Task UpdateIInvoiceReturn(Invoice invoice, Invoice invoiceDetached, ServiceModel.InvoiceDTO invoiceDto)
        {
            var lsReturn = await GetInvoiceReturn(invoice, invoiceDetached);
            if (invoiceDto.InvoiceDetails != null && invoiceDto.InvoiceDetails.Any())
            {
                foreach (var item in invoiceDto.InvoiceDetails)
                {
                    item.ReturnQuantity = lsReturn.ContainsKey(item.ProductId) ? lsReturn[item.ProductId] : 0;
                }
            }
        }

        private async Task<Dictionary<long, double>> GetInvoiceReturn(Invoice invoice, Invoice invoiceDetached)
        {
            var lstProductIds = invoiceDetached.InvoiceDetails?.Select(p => p.ProductId).ToList();
            if (lstProductIds == null || !lstProductIds.Any())
            {
                return new Dictionary<long, double>();
            }

            var lsReturnDetail = await ReturnDetailService.GetAll()
                .Where(p => lstProductIds
                    .Contains(p.ProductId)).ToListAsync();
            var lst = await ReturnService.GetByInvoiceIdAsync(invoice.Id);
            var query = from r in lst
                        join rd in lsReturnDetail
                        on r.Id equals rd.ReturnId
                        select rd;
            var lsReturn = query.GroupBy(g => g.ProductId).Select(g => new
            {
                ProductId = g.Key,
                ReturnQuantity = g.Sum(v => v.Quantity)
            }).ToDictionary(p => p.ProductId, e => e.ReturnQuantity);

            return lsReturn;
        }

        private async Task UpdateInvoiceSaleChannel(ServiceModel.InvoiceDTO invoiceDto)
        {
            var saleChannel = await SaleChannelService.GetAllAsync()
                            .FirstOrDefaultAsync(x => x.Id == invoiceDto.SaleChannelId);
            if (saleChannel != null)
            {
                invoiceDto.SaleChannel = saleChannel.ConvertTo<SaleChannelDto>();
            }
        }

        private async Task UpdateInvoiceDeliveryInfo(Invoice invoice, Invoice invoiceDetached)
        {
            if (invoice.DeliveryInfoes == null || invoice.DeliveryInfoes.Count <= 0)
            {
                return;
            }

            invoiceDetached.CodNeedPayment = 0;
            invoiceDetached.DeliveryInfo = DeliveryInfoService
                .DetachByClone(invoice.DeliveryInfoes.ToList(), new[] { "PartnerDelivery", "DeliveryPackage" })
                .FirstOrDefault(x => x.RetailerId == CurrentRetailerId && x.IsCurrent == true);

            if (invoiceDetached.DeliveryInfo != null)
            {
                var codNeedPayment = (invoiceDetached.NewInvoiceTotal >= 0 ? invoiceDetached.NewInvoiceTotal : invoiceDetached.Total) - invoiceDetached.TotalPayment;
                invoiceDetached.InvoiceDelivery = await InvoiceDeliveryService.ConvertToInvoiceDelivery(invoiceDetached.DeliveryInfo);
                invoiceDetached.CodNeedPayment = IsNotNeedCodPayment(invoiceDetached) ? 0 : codNeedPayment >= 0 ? codNeedPayment : 0;
            }
            else
            {
                invoiceDetached.InvoiceDelivery = null;
            }

            invoiceDetached.DeliveryInfoes = null;
            invoiceDetached.InvoiceDeliveries = null;
        }

        private async Task<Invoice> GetInVoiceDetail(GetInvoice request)
        {
            if (request.Id <= 0 && string.IsNullOrEmpty(request.Code))
            {
                throw new KvValidateInvoiceException("Mã hóa đơn không hợp lệ");
            }

            var invoice = request.Id > 0
                ? await InvoiceService.GetByIdAsync(request.Id)
                : await InvoiceService.GetAll().FirstOrDefaultAsync(x => x.Code == request.Code.Trim());

            if (invoice == null)
            {
                throw new KvValidateInvoiceException(KVMessage.NotFound);
            }

            return invoice;
        }

        private void UpdateInvoiceDetails(Invoice invoice, ServiceModel.InvoiceDTO invoiceDto)
        {
            invoiceDto.InvoiceDetails = (invoice.InvoiceDetails ?? throw new KvValidateInvoiceException(KVMessage.NotFound)).Select(x => new ServiceModel.InvoiceDetailDTO()
            {
                ProductId = x.ProductId,
                Discount = x.ViewDiscount != null && x.ViewDiscount < NumberHelper.KvConstTolerance ? 0 : NumberHelper.RoundProductPrice(x.Discount),
                DiscountRatio = x.DiscountRatio,
                Price = NumberHelper.RoundProductPrice(x.ViewDiscount != null && x.ViewDiscount < NumberHelper.KvConstTolerance ? x.Price - x.ViewDiscount : x.Price) ?? 0,
                ProductCode = x.ProductCode,
                ProductName = x.ProductName,
                Quantity = x.Quantity,
                SubTotal = x.SubTotal,
                UsePoint = x.UsePoint,
                Note = x.Note,
                SerialNumbers = x.SerialNumbers
            }).ToList();
        }

        private void UpdateInvoiceDelivery(Invoice invoiceDetached, ServiceModel.InvoiceDTO invoiceDto)
        {
            if (invoiceDetached.InvoiceDelivery != null)
            {
                invoiceDto.InvoiceDelivery = invoiceDetached.InvoiceDelivery.ConvertTo<InvoiceDeliveryDTO>();
                invoiceDto.InvoiceDelivery.StatusValue = EnumHelper.ToDescription((DeliveryStatus)invoiceDetached.InvoiceDelivery.Status);
                invoiceDto.InvoiceDelivery.ServiceType = invoiceDetached.InvoiceDelivery.ServiceCode;
                invoiceDto.InvoiceDelivery.ServiceTypeText = invoiceDetached.InvoiceDelivery.ServiceCodeText;
            }
        }

        private void UpdateOrderpayment(Invoice invoice, Invoice invoiceDetached)
        {
            if (invoice.Order?.Payments != null && invoice.Order.Payments.Count > 0 && invoiceDetached.Order != null)
            {
                invoiceDetached.Order.Payments = PaymentService.DetachByClone(invoice.Order.Payments.ToList());
            }
        }

        private void UpdateInvoiceSurchanges(Invoice invoice, Invoice invoiceDetached)
        {
            if (invoice.InvoiceOrderSurcharges != null && invoice.InvoiceOrderSurcharges.Count > 0)
            {
                invoiceDetached.InvoiceOrderSurcharges = InvoiceOrderSurchargeService.DetachByClone(invoice.InvoiceOrderSurcharges.ToList());
            }
        }

        private void UpdateInvoiceDetails(Invoice invoice, Invoice invoiceDetached)
        {
            if (invoice.InvoiceDetails != null && invoice.InvoiceDetails.Count > 0)
            {
                invoiceDetached.InvoiceDetails = InvoiceDetailService.DetachByClone(invoice.InvoiceDetails.ToList(), new[] { "Product" });
            }
        }

        private bool IsNotNeedCodPayment(Invoice invoiceDetached)
        {
            return (invoiceDetached.InvoiceDelivery.UsingPriceCod ?? 0) == 0 ||
                    invoiceDetached.Status != (byte)InvoiceState.Pending ||
                    invoiceDetached.DeliveryInfo.Status == (byte)DeliveryStatus.Void ||
                    invoiceDetached.DeliveryInfo.Status == (byte)DeliveryStatus.Returned;
        }
    }
}
