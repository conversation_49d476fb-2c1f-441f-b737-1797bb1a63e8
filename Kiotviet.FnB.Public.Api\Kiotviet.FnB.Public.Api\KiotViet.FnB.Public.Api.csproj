﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\packages\librdkafka.redist.1.1.0\build\librdkafka.redist.props" Condition="Exists('..\..\packages\librdkafka.redist.1.1.0\build\librdkafka.redist.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <OnBeforePackageUsingManifest>AddCustomSkipRules</OnBeforePackageUsingManifest>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6F86FC59-9DE7-4BF5-8D11-CB7E99DA29FC}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>KiotViet.FnB.Public.Api</RootNamespace>
    <AssemblyName>KiotViet.FnB.Public.Api</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <RestorePackages>true</RestorePackages>
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <Target Name="AddCustomSkipRules">
    <ItemGroup>
      <MsDeploySkipRules Include="SkipDeleteWebConfig">
        <SkipAction>Delete</SkipAction>
        <ObjectName>filePath</ObjectName>
        <AbsolutePath>web.config</AbsolutePath>
        <XPath>
        </XPath>
      </MsDeploySkipRules>
      <MsDeploySkipRules Include="SkipDeleteWebConfig">
        <SkipAction>Delete</SkipAction>
        <ObjectName>filePath</ObjectName>
        <AbsolutePath>favicon.ico</AbsolutePath>
        <XPath>
        </XPath>
      </MsDeploySkipRules>
      <MsDeploySkipRules Include="SkipDeleteLogData">
        <SkipAction>Delete</SkipAction>
        <ObjectName>filePath</ObjectName>
        <AbsolutePath>Logging\*.*</AbsolutePath>
        <XPath>
        </XPath>
      </MsDeploySkipRules>
      <MsDeploySkipRules Include="SkipDeleteLogData">
        <SkipAction>Delete</SkipAction>
        <ObjectName>dirPath</ObjectName>
        <AbsolutePath>Logging</AbsolutePath>
        <XPath>
        </XPath>
      </MsDeploySkipRules>
    </ItemGroup>
  </Target>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PublishDatabases>true</PublishDatabases>
    <DeployEncryptKey>Encryption password is used only if any deployment setting is marked as secure</DeployEncryptKey>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.4.6.1\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Mvc, Version=4.0.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.Mvc5.4.0.2\lib\net45\Autofac.Integration.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Confluent.Kafka, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Confluent.Kafka.1.1.0\lib\net46\Confluent.Kafka.dll</HintPath>
    </Reference>
    <Reference Include="Consul, Version=1.7.14.2, Culture=neutral, PublicKeyToken=20a6ad9a81df1d95, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Consul.1.7.14.2\lib\net461\Consul.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.BulkInsert, Version=6.0.2.8, Culture=neutral, PublicKeyToken=630a17433349cb76, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.BulkInsert-ef6.6.0.2.8\lib\Net45\EntityFramework.BulkInsert.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.MappingAPI, Version=6.0.0.7, Culture=neutral, PublicKeyToken=7ee2e825d201459e, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.MappingAPI.6.0.0.7\lib\net45\EntityFramework.MappingAPI.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Audit.Model, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.Audit.Model.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Auth">
      <HintPath>..\..\Component\KiotViet.Auth.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.CommandDispatcher, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.CommandDispatcher.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Exceptions, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.Exceptions.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.FeatureToggle">
      <HintPath>..\..\Component\KiotViet.FeatureToggle.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.FirebaseRealTimeDatabase, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.FirebaseRealTimeDatabase.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.KvSerilog">
      <HintPath>..\..\Component\KiotViet.KvSerilog.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.MongoDb">
      <HintPath>..\..\Component\KiotViet.MongoDb.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.MongoServices">
      <HintPath>..\..\Component\KiotViet.MongoServices.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Persistence">
      <HintPath>..\..\Component\KiotViet.Persistence.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.RedisServices">
      <HintPath>..\..\Component\KiotViet.RedisServices.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Reports, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.Reports.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Resources, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Component\KiotViet.Resources.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Services">
      <HintPath>..\..\Component\KiotViet.Services.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.ServiceUpdateMultiCache.ServiceInterface">
      <HintPath>..\..\Component\KiotViet.ServiceUpdateMultiCache.ServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.ServiceUpdateMultiCache.ServiceModel">
      <HintPath>..\..\Component\KiotViet.ServiceUpdateMultiCache.ServiceModel.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Utilities">
      <HintPath>..\..\Component\KiotViet.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="KiotViet.Web.Api">
      <HintPath>..\..\Component\KiotViet.Web.Api.dll</HintPath>
    </Reference>
    <Reference Include="KiotVietFnB.BuildInfoTracing, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\KiotVietFnB.BuildInfoTracing.1.0.0\lib\netstandard2.0\KiotVietFnB.BuildInfoTracing.dll</HintPath>
    </Reference>
    <Reference Include="KiotVietFnB.BuildInfoTracing.ServiceStackApi, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\KiotVietFnB.BuildInfoTracing.ServiceStackApi.1.0.0\lib\net462\KiotVietFnB.BuildInfoTracing.ServiceStackApi.dll</HintPath>
    </Reference>
    <Reference Include="KiotVietFnB.EntityFramework.Extensions, Version=1.2.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\KiotVietFnB.EntityFramework.Extensions.1.2.4\lib\netstandard2.0\KiotVietFnB.EntityFramework.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="KiotVietFnB.EntityFramework.Extensions.SqlServer, Version=1.2.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\KiotVietFnB.EntityFramework.Extensions.SqlServer.1.2.4\lib\net462\KiotVietFnB.EntityFramework.Extensions.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="KvFnBConsul, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\KvFnBConsul.1.3.0\lib\net462\KvFnBConsul.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit, Version=1.1.2.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\..\packages\LINQKit.1.1.2\lib\net45\LinqKit.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Mehdime.Entity, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Mehdime.Entity.1.0.0\lib\Mehdime.Entity.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.3.0.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Owin.Host.SystemWeb.3.0.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Bson, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MongoDB.Bson.2.2.4\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MongoDB.Driver.2.2.4\lib\net45\MongoDB.Driver.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Core, Version=********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\MongoDB.Driver.Core.2.2.4\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="RabbitMQ.Client, Version=3.6.9.0, Culture=neutral, PublicKeyToken=89e7d7c5feba84ce, processorArchitecture=MSIL">
      <HintPath>..\..\packages\RabbitMQ.Client.3.6.9\lib\net45\RabbitMQ.Client.dll</HintPath>
    </Reference>
    <Reference Include="RustFlakes, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\RustFlakes.2.0.0\lib\netstandard2.0\RustFlakes.dll</HintPath>
    </Reference>
    <Reference Include="Serilog, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.2.10.0\lib\net46\Serilog.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.Environment, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Enrichers.Environment.2.1.2\lib\net45\Serilog.Enrichers.Environment.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.HttpContextData, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Enrichers.HttpContextData.0.1.2\lib\net45\Serilog.Enrichers.HttpContextData.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.Memory, Version=1.0.4.0, Culture=neutral, PublicKeyToken=b7bed4405496f503, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Enrichers.Memory.1.0.4\lib\net45\Serilog.Enrichers.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.Process, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Enrichers.Process.2.0.1\lib\net45\Serilog.Enrichers.Process.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Enrichers.Thread, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Enrichers.Thread.3.0.0\lib\net45\Serilog.Enrichers.Thread.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Formatting.Compact, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Formatting.Compact.1.0.0\lib\net45\Serilog.Formatting.Compact.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Settings.AppSettings, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Settings.AppSettings.2.1.2\lib\net45\Serilog.Settings.AppSettings.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Async, Version=*******, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.Async.1.3.0\lib\net45\Serilog.Sinks.Async.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.File, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.File.4.0.0\lib\net45\Serilog.Sinks.File.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.PeriodicBatching, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.PeriodicBatching.2.1.1\lib\net45\Serilog.Sinks.PeriodicBatching.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.RollingFile, Version=2.0.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.RollingFile.3.3.0\lib\net45\Serilog.Sinks.RollingFile.dll</HintPath>
    </Reference>
    <Reference Include="Serilog.Sinks.Seq, Version=3.4.0.0, Culture=neutral, PublicKeyToken=24c2f752a8e58a10, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Serilog.Sinks.Seq.3.4.0\lib\net45\Serilog.Sinks.Seq.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.5.4.0\lib\net45\ServiceStack.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Api.Swagger, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Api.Swagger.5.4.0\lib\net45\ServiceStack.Api.Swagger.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Client, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Client.5.4.0\lib\net45\ServiceStack.Client.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Common, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Common.5.4.0\lib\net45\ServiceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Interfaces.5.4.0\lib\net45\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Logging.Log4Net, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Logging.Log4Net.5.4.0\lib\net45\ServiceStack.Logging.Log4Net.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Logging.Serilog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Logging.Serilog.5.4.0\lib\net45\ServiceStack.Logging.Serilog.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.OrmLite, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.OrmLite.5.4.0\lib\net45\ServiceStack.OrmLite.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Razor, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Razor.5.4.0\lib\net45\ServiceStack.Razor.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Redis, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Redis.5.4.0\lib\net45\ServiceStack.Redis.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Request.Correlation, Version=5.0.0.12, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Request.Correlation.5.0.0\lib\net462\ServiceStack.Request.Correlation.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Server, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Server.5.4.0\lib\net45\ServiceStack.Server.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Text, Version=5.0.0.0, Culture=neutral, PublicKeyToken=02c12cbda47e6587, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ServiceStack.Text.5.4.0\lib\net45\ServiceStack.Text.dll</HintPath>
    </Reference>
    <Reference Include="Superpower, Version=*******, Culture=neutral, PublicKeyToken=aec39280ded1b3a7, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Superpower.1.1.0\lib\net45\Superpower.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Buffers.4.4.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Configuration.ConfigurationManager.4.7.0\lib\net461\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Memory, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Memory.4.5.1\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Runtime.CompilerServices.Unsafe.4.5.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Security.Permissions.4.7.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.Razor.3.2.6\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WindowsBase" />
    <Reference Include="Z.EntityFramework.Extensions, Version=3.12.14.0, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Z.EntityFramework.Extensions.3.12.14\lib\net45\Z.EntityFramework.Extensions.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="api-docs.html" />
    <Content Include="css\app.css" />
    <None Include="default.cshtml" />
    <Content Include="js\app.js" />
    <Content Include="js\hello\controllers.js" />
    <Content Include="js\navigation\controllers.js" />
    <Content Include="partials\404.html" />
    <Content Include="partials\hello\hello.html" />
    <Content Include="partials\partial1.html" />
    <Content Include="partials\partial2.html" />
    <None Include="packages.config" />
    <None Include="Properties\PublishProfiles\publicweb.pubxml" />
    <None Include="Scripts\_references.js" />
    <Content Include="swagger-ui-bootstrap\index.html" />
    <Content Include="swagger-ui-bootstrap\jiko.js" />
    <Content Include="swagger-ui-bootstrap\swagger-like-template.html" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-700.eot" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-700.ttf" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-700.woff" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-700.woff2" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-regular.eot" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-regular.ttf" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-regular.woff" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-regular.woff2" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Global.asax" />
    <Content Include="swagger-ui\css\reset.css" />
    <Content Include="swagger-ui\css\screen.css" />
    <Content Include="swagger-ui\css\typography.css" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-700.svg" />
    <Content Include="swagger-ui\fonts\droid-sans-v6-latin-regular.svg" />
    <Content Include="swagger-ui\images\explorer_icons.png" />
    <Content Include="swagger-ui\images\logo_small.png" />
    <Content Include="swagger-ui\images\pet_store_api.png" />
    <Content Include="swagger-ui\images\throbber.gif" />
    <Content Include="swagger-ui\images\wordnik_api.png" />
    <Content Include="swagger-ui\index.html" />
    <Content Include="swagger-ui\lib\backbone-min.js" />
    <Content Include="swagger-ui\lib\handlebars-2.0.0.js" />
    <Content Include="swagger-ui\lib\highlight.7.3.pack.js" />
    <Content Include="swagger-ui\lib\jquery-1.8.0.min.js" />
    <Content Include="swagger-ui\lib\jquery.ba-bbq.min.js" />
    <Content Include="swagger-ui\lib\jquery.slideto.min.js" />
    <Content Include="swagger-ui\lib\jquery.wiggle.min.js" />
    <Content Include="swagger-ui\lib\marked.js" />
    <Content Include="swagger-ui\lib\shred.bundle.js" />
    <Content Include="swagger-ui\lib\shred\content.js" />
    <Content Include="swagger-ui\lib\swagger-client.js" />
    <Content Include="swagger-ui\lib\swagger-oauth.js" />
    <Content Include="swagger-ui\lib\underscore-min.js" />
    <Content Include="swagger-ui\o2c.html" />
    <Content Include="swagger-ui\patch.js" />
    <Content Include="swagger-ui\swagger-ui.js" />
    <Content Include="swagger-ui\swagger-ui.min.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppHost.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Kiotviet.FnB.Public.Api.ServiceInterface\KiotViet.FnB.Public.Api.ServiceInterface.csproj">
      <Project>{582dfb0f-f84d-4e43-9bca-2a5a86600b7a}</Project>
      <Name>KiotViet.FnB.Public.Api.ServiceInterface</Name>
    </ProjectReference>
    <ProjectReference Include="..\Kiotviet.FnB.Public.Api.ServiceModel\KiotViet.FnB.Public.Api.ServiceModel.csproj">
      <Project>{08bef328-ab7d-48d3-800d-48acc75c7914}</Project>
      <Name>KiotViet.FnB.Public.Api.ServiceModel</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="img\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Import Project="..\..\Component\BuildCommon.targets" Condition="Exists('..\..\Component\BuildCommon.targets')" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>43724</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:8005/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\MSBuildTasks.*********\build\MSBuildTasks.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\MSBuildTasks.*********\build\MSBuildTasks.targets'))" />
    <Error Condition="!Exists('..\..\packages\librdkafka.redist.1.1.0\build\librdkafka.redist.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\librdkafka.redist.1.1.0\build\librdkafka.redist.props'))" />
    <Error Condition="!Exists('..\..\packages\KiotVietFnB.BuildInfoTracing.1.0.0\build\KiotVietFnB.BuildInfoTracing.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\KiotVietFnB.BuildInfoTracing.1.0.0\build\KiotVietFnB.BuildInfoTracing.targets'))" />
    <Error Condition="!Exists('..\..\packages\KiotVietFnB.BuildInfoTracing.ServiceStackApi.1.0.0\build\KiotVietFnB.BuildInfoTracing.ServiceStackApi.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\KiotVietFnB.BuildInfoTracing.ServiceStackApi.1.0.0\build\KiotVietFnB.BuildInfoTracing.ServiceStackApi.targets'))" />
  </Target>
  <Import Project="..\..\packages\MSBuildTasks.*********\build\MSBuildTasks.targets" Condition="Exists('..\..\packages\MSBuildTasks.*********\build\MSBuildTasks.targets')" />
  <Import Project="..\..\packages\KiotVietFnB.BuildInfoTracing.1.0.0\build\KiotVietFnB.BuildInfoTracing.targets" Condition="Exists('..\..\packages\KiotVietFnB.BuildInfoTracing.1.0.0\build\KiotVietFnB.BuildInfoTracing.targets')" />
  <Import Project="..\..\packages\KiotVietFnB.BuildInfoTracing.ServiceStackApi.1.0.0\build\KiotVietFnB.BuildInfoTracing.ServiceStackApi.targets" Condition="Exists('..\..\packages\KiotVietFnB.BuildInfoTracing.ServiceStackApi.1.0.0\build\KiotVietFnB.BuildInfoTracing.ServiceStackApi.targets')" />
</Project>