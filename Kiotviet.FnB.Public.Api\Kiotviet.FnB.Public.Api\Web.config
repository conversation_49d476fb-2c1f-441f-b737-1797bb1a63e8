﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <sectionGroup name="system.web.webPages.razor" type="System.Web.WebPages.Razor.Configuration.RazorWebSectionGroup, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
      <section name="host" type="System.Web.WebPages.Razor.Configuration.HostSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
      <section name="pages" type="System.Web.WebPages.Razor.Configuration.RazorPagesSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings>
    <add key="owin:AutomaticAppStartup" value="false" />
    <add key="defaultPageSize" value="50" />
    <add key="maxPageSize" value="100" />
    <add key="DebugMode" value="true" />
    <add key="webpages:Enabled" value="false" />
    <add key="vs:EnableBrowserLink" value="false" />
    <add key="UploadFolderPath" value="~/Content/Upload/" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="SiteFormat" value="{RetailerCode}.localhost.com" />
    <add key="IgnoreSubdomain" value="www,admin,api,administrator,xxx,application,app,blog,cpanel" />
    <add key="SiteAdminFormat" value="cpanel." />
    <add key="SiteApiFormat" value="api." />
    <add key="SiteInfoFormat" value="www." />
    <add key="RetailerNotFound" value="http://www.kiotviet.com/cua-hang-khong-ton-tai/" />
    <add key="DateExpired" value="15" />
    <add key="MvcMailer.BaseURL" value="" />
    <add key="EnabledShareDataBase" value="True" />
    <add key="servicestack:license" value="6617-e1JlZjo2NjE3LE5hbWU6Q2l0aWdvIFNvZnR3YXJlIENvLixUeXBlOkluZGllLE1ldGE6MCxIYXNoOkdqM1lUQVhQS05YOWFwYlY3bDA4RWRnc0taWEpoUDdZL0Q0SzZqOENxMXBYQmhLUzVlbDR6aTJXNU1lQzhnWGdTYjA0cDZoMzZuaDZuTE5MbHQ4RG5zUlU3bUpVT0NoejI2a2IvQVVSTzRtYzF4NTkzYTBja1BiZVRMVmFIOU55TFErT1VaQ3NKcm5yd243elNWVDR6ZmkyZUdKUzZNbDFqRkQ2cWNnTEliST0sRXhwaXJ5OjIwMTktMTAtMTZ9" />
    <add key="microsoft.visualstudio.teamsystems.backupinfo" value="1;web.config.backup" />
    <!-- Key for google drive -->
    <add key="parent_folder_id" value="0BwROeiI8-4xSfk1DOERCVlJFM2dGeE1YQnYwZjFpLWljNUg3ZDhNRmhETF9CeldyOEFydGs" />
    <add key="secure_account_email" value="<EMAIL>" />
    <add key="secure_p12_file" value="~/kiotviet.p12" />
    <add key="AuditTrailDataSet" value="kiotviet_release" />
    <add key="MongoDBConnectionStr" value="mongodb://*************:27017/kiotviet?maxPoolSize=10000" />
    <add key="google_tracking_number" value="UA-********-1" />
    <!--
    <add key="MongoDBConnectionStr" value="mongodb://localhost:27017/?maxPoolSize=10000" />
    -->
    <add key="thumbnail_size" value="280" />
    <!-- End Key for google drive -->
    <!-- Key for Redis -->
    <!--<add key="redis.cache" value="{Servers:**************:6379,SentinelMasterName:senmaster1,DbNumber:6,MaxPoolSize:100,AuthPass:K10tV13t#Red1s,IsSentinel:false}"/>
    <add key="redis.messagequeue" value="{Servers:**************:6379,SentinelMasterName:senmaster1,DbNumber:6,MaxPoolSize:100,AuthPass:K10tV13t#Red1s,IsSentinel:false}"/>-->
    <add key="redis.cache" value="{Servers:localhost:6379,SentinelMasterName:senmaster,DbNumber:11,MaxPoolSize:100,IsSentinel:false}" />
    <add key="redis.messagequeue" value="{Servers:localhost:6379,SentinelMasterName:senmaster,DbNumber:11,MaxPoolSize:100,IsSentinel:false}" />
    <!-- End Redis Key -->
    <!--End Sentinel Key-->
    <!-- End Redis Key -->
    <!-- Key for ES -->
    <add key="EnableEsIntegration" value="false" />
    <add key="RabbitVhostEs" value="ES" />
    <!-- End ES Key -->
    <!-- Key for Amazon S3 -->
    <add key="aws_access_key_id" value="********************" />
    <add key="aws_secret_access_key" value="V6t+tbCBUtbWVqqfhv146Nxy+UurCKTJp/gfKqBa" />
    <add key="aws_cache_control_header" value="public, max-age=86400, no-transform" />
    <add key="aws_bucket_name" value="sanphamtest" />
    <add key="aws_cloudfront_url" value="//d1mlba67mwwtw4.cloudfront.net/" />
    <add key="aws_max_photo_size" value="2097152" />
    <add key="aws_max_photo_width" value="800" />
    <add key="aws_max_photo_height" value="600" />
    <add key="aws_max_logo_size" value="1048576" />
    <add key="aws_max_logo_width" value="200" />
    <add key="aws_max_logo_height" value="200" />
    <add key="max_upload_file_size" value="10485760" />
    <!-- Begin Rabbit -->
    <add key="RabbitHostname" value="*************" />
    <add key="RabbitNotification" value="*************" />
    <add key="RabbitPort" value="55672" />
    <add key="RabbitNotificationPort" value="55672" />
    <add key="RabbitUsername" value="recovery" />
    <add key="RabbitPassword" value="recovery" />
    <add key="RabbitRequestedHeartbeat" value="10" />
    <add key="RabbitVirtualHost" value="9006" />
    <add key="InventoryImpactPerTrans" value="100" />
    <add key="KVClearRetailerQueue" value="KVClearRetailer_9006" />
    <add key="KVSmsMailQueue" value="KVSmsMail_9006" />
    <add key="KVAuditTrailQueue" value="KVAuditTrail_9006" />
    <add key="KVNotificationQueue" value="KVNotification_FNB_9006" />
    <add key="KVImportProductQueue" value="KVImportProduct_9006" />
    <add key="KVImportCustomerQueue" value="KVImportCustomer_9006" />
    <add key="KVRealTimeDdQueue" value="KVRealTimeDdQueue_9006" />
    <!-- End Rabbit-->
    <add key="HangfireRedisConnStr" value="*************:6379,abortConnect=false,password=K10tV13t#Red1s,defaultDatabase=8" />
    <add key="HangfireRedisPrefix" value="hangfire:development:" />
    <add key="HangfireRetryTimes" value="2" />
    <add key="AdsArticle" value="http://www.kiotviet.vn/?p=10994" />
    <add key="ReportLimitRows" value="200" />
    <!-- Begin threshold of report -->
    <add key="ReportThreshold" value="200" />
    <!-- Begin threshold -->
    <add key="ReCaptcha:SiteKey" value="6LfmoRMTAAAAAPUe_ErrIBK-iOObmOZIzBKS77Rd" />
    <add key="ReCaptcha:SecretKey" value="6LfmoRMTAAAAABMqKUqhsTOEhsFs801F1RQCt0dS" />
    <add key="UseCacheReport" value="false" />
    <add key="ExportDetailLimit" value="2000" />
    <add key="SignalIrSqlConnection" value="Server=*************;Database=KVSignalIR;User Id=kiotvietdev;Password = ********$6162;" />
    <add key="IsUseSignalRSqlConnection" value="false" />
    <!--RateLimit for Public API-->
    <add key="RequestLimitApi" value="5000" />
    <!--Per Hours-->
    <add key="RequestTimeInSecondsApi" value="1" />
    <!-- Add Ip for Cpanel User -->
    <add key="GlobalIpLogin" value="127.0.0.1" />
    <!-- Add Black List IP-->
    <add key="BlackListIp" value="" />
    <add key="WhiteListIp" value="127.0.0.1;*************" />
    <add key="GlobalApiAccessKey" value="K1oTv!3T@N0j5" />
    <add key="ReportAppId" value="KiotVietReport" />
    <add key="ReportAppCachePath" value="D:\Temp" />
    <add key="HostName" value="localhost.com" />
    <!--Limit KiotMail-->
    <add key="AdvanceLimitKiotMail" value="100" />
    <add key="BasicLimitKiotMail" value="100" />
    <add key="TrialLimitKiotMail" value="5" />
    <!--External MailService Config-->
    <add key="KiotMailServerList" value="email-smtp.us-east-1.amazonaws.com;email-smtp.us-west-2.amazonaws.com;email-smtp.eu-west-1.amazonaws.com" />
    <add key="KiotMailPort" value="587" />
    <add key="KiotMailUseSsl" value="true" />
    <add key="KiotMailUsernameCertify" value="AKIAJTUWFRBKXDAXAO5A" />
    <add key="KiotMailPasswordCertify" value="AtyopSNkA++agDn/dh0JMiVlJkIeJW5Rk7lGlKmsZTVe" />
    <!--Vietguys api-->
    <add key="VietguysBrandApi" value="http://sms.vietguys.biz/kiotviet/api_load_brand.php" />
    <add key="VietguysRegisterApi" value="http://sms.vietguys.biz/kiotviet/reg.php" />
    <add key="VietguysSendSmsApi" value="https://sms.vietguys.biz/api/send.php" />
    <add key="ReferApi" value="https://www.kiotviet.vn/meeting-register/" />
    <add key="ExcludedRetailers" value="taphoa,banhang5" />
    <!--Session Expire-->
    <!--Hours-->
    <add key="session_expire" value="8" />
    <!--Days-->
    <add key="permanent_session_expiry" value="28" />
    <!--Mobile api-->
    <add key="AndroidVersion" value="{version: '1.0.0.1', code: 16052301, URL: 'https://play.google.com/store/apps/details?id=net.citigo.kiotviet'}" />
    <add key="iOSVersion" value="{version: '1.0.0.1', code: '4.9', URL: 'https://itunes.apple.com/us/app/kiotviet-pos-phan-mem-quan/id1080531532?ls=1&amp;mt=8'}" />
    <add key="DefaultNotificationSetting" value="[{'EventType':'Order_Create','IsActive':true},{'EventType':'Order_Notify_Kitchen','IsActive':true},{'EventType':'Return_Create','IsActive':true},{'EventType':'Customer_Birthday','IsActive':true},{'EventType':'Product_OnHand','IsActive':false}]" />
    <!--JWT Key-->
    <add key="jwt.HashAlgorithm" value="RS256" />
    <add key="jwt.RequireHashAlgorithm" value="False" />
    <add key="jwt.PublicKeyXml" value="&lt;RSAKeyValue&gt;&lt;Modulus&gt;0G3HHCjKl97MmL+t1jwGn804/bCabR9XLZrUUftzIUaiCoCbbi3sr3ZUTnvmLWMs0WUIpvISLfp6CgahAj+HSWSdbl1BmtfsVPlNjj9N6D0fhfvwXR9yK9crDkz+NV2Wz8YHoZftGBjjjegkxUQXx9m4ydoKGJIuJxAznzvi+FZqX+N2B+sgcIW5lPAwq6MpDINOSBLzl5wOprFG9amMhLfYVoD0ltUnLsJc4xsasC6YvSKXt1K1fHORp10JpTYHuR+ElhuRXseYRGIe8lrc9Ls+FnXMxN09cCRoIR+9Jwotr4gxaMhpuqhOMg6M4TXrjd+CLWwcnkS/rYAHlTEJpQ==&lt;/Modulus&gt;&lt;Exponent&gt;AQAB&lt;/Exponent&gt;&lt;/RSAKeyValue&gt;" />
    <add key="jwt.EncryptPayload" value="False" />
    <add key="jwt.RequireSecureConnection" value="False" />
    <!--End JWT Key-->
    <!-- Webhook-->
    <add key="webhook_server" value="http://webhook-fnb.localhost.com/" />
    <add key="webhook_username" value="<EMAIL>" />
    <add key="webhook_password" value="123Aa@" />
    <!--ZZZ License-->
    <add key="Z_EntityFramework_Extensions_LicenseName" value="1005;101-Citigo" />
    <add key="Z_EntityFramework_Extensions_LicenseKey" value="781100b5-3e55-4d6d-e40c-253c095ffe31" />
    <!--End ZZZ License-->
    <!-- SeriLog-->
    <add key="serilog:write-to:RollingFile.pathlog" value="~\Logging\log-{Date}.json" />
    <add key="serilog:write-to:RollingFile.bufferSize" value="500" />
    <add key="serilog:appName" value="FnB-PublicAPI" />
    <add key="serilog:IsActive" value="true" />
    <add key="serilog:ExpressionFilter" value="@Level = 'Information' OR @Level = 'Error' OR @Level = 'Fatal'" />
    <add key="serilog:IsIsWriteRequest" value="false" />
    <!-- Begin ElasticSearch -->
    <add key="EnableEsIntegration" value="false" />
    <add key="RabbitVhostEs" value="ES" />
    <!-- End ElasticSearch -->
    <!-- Begin Tk v3 -->
    <add key="KafkaIp" value="*************:9092" />
    <add key="KafkaPrefix" value="-fnb-development" />
    <!-- ServiceStack OrmlLite -->
    <add key="MasterConnStr" value="Server=*************;Database=KiotVietMasterFnBRelease;Persist security info=True;User Id=debugger;Password=********$6162;MultipleActiveResultSets=True;Max Pool Size=10000;" />
    <!-- RabbitMq -->
    <add key="RabbitHostTk3" value="*************" />
    <add key="RabbitPortTk3" value="5670" />
    <add key="RabbitUsernameTk3" value="recovery" />
    <add key="RabbitPasswordTk3" value="recovery" />
    <add key="RabbitHeartbeatTk3" value="10" />
    <add key="RabbitVHostTk3" value="tkv3-fnb-development" />
    <!-- End Tk v3 -->
    <!-- Autofact -->
    <add key="IsOptimizeAutofac" value="true" />
    <!-- End autofact -->
  
    <add key="ApplyQueryTagging" value="true" />

	  <add key="LimitPurchaseDateByDays" value="8" /> <!-- GetInvoiceList Filter Purchase Date Diff With Modified Date -->
  
    <!--Consul service registry-->
    <add key="ConsulAddress" value="http://localhost:8500" />
    <add key="ConsulToken" value="" />
    <add key="ConsulServiceId" value="api_public" />
    <add key="ConsulServiceName" value="api_public" />
    <add key="ConsulServiceAddress" value="http://public-api-dev.citigo.io" />
    <add key="ConsulServicePort" value="443" />
    <add key="ConsulTags" value="public_api" />
    <add key="ConsulAgentCheckIntervalSeconds" value="15" />
    <add key="ConsulAgentCheckTimeoutSeconds" value="5" />
    <add key="ConsulAgentCheckDeregisterCriticalServiceAfterSeconds" value="30" />
    <add key="ConsulAgentCheckHealthHttpApi" value="ping" />
    <add key="ConsulAgentCheckHealthHttps" value="false" />
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
  <system.web>
    <customErrors mode="Off" />
    <compilation targetFramework="4.6.2" debug="true">
    <buildProviders>
        <add extension=".cshtml" type="ServiceStack.Razor.CSharpRazorBuildProvider, ServiceStack.Razor" />
      </buildProviders></compilation>
    <httpRuntime targetFramework="4.5" />
    <httpHandlers>
      <add path="*" type="ServiceStack.HttpHandlerFactory, ServiceStack" verb="*" />
    </httpHandlers>
  </system.web>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false" />
    <urlCompression doStaticCompression="true" doDynamicCompression="false" />
    <handlers>
      <add path="*" name="ServiceStack.Factory" type="ServiceStack.HttpHandlerFactory, ServiceStack" verb="*" preCondition="integratedMode" resourceType="Unspecified" allowPathInfo="true" />
    </handlers>
    <defaultDocument>
      <files>
        <clear />
        <add value="default.cshtml" />
      </files>
    </defaultDocument>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.2.15.0" newVersion="1.2.15.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Autofac" publicKeyToken="17863af14b0044da" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.6.1.0" newVersion="4.6.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Autofac.Integration.Mvc" publicKeyToken="17863af14b0044da" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.0" newVersion="4.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="LinqKit" publicKeyToken="bc217f8844052a91" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.1.2.0" newVersion="1.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Z.EntityFramework.Extensions" publicKeyToken="59b66d028979105b" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.12.14.0" newVersion="3.12.14.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="RabbitMQ.Client" publicKeyToken="89e7d7c5feba84ce" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.6.9.0" newVersion="3.6.9.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
    <connectionStrings>
    <add name="KvIdentityEntities" connectionString="metadata=res://*/KvIdentity.csdl|res://*/KvIdentity.ssdl|res://*/KvIdentity.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=dc2d-fnb-mssql-01.citigo.io;initial catalog=KvIdentityFnB9534;user id=kiotvietdev;password=********$6162;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="KVMasterDataEntities" connectionString="metadata=res://*/kvMasterData.csdl|res://*/kvMasterData.ssdl|res://*/kvMasterData.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=dc2d-fnb-mssql-01.citigo.io;initial catalog=KiotVietMasterFnB9534;persist security info=True;user id=kiotvietdev;password=********$6162;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="KVEntities" connectionString="metadata=res://*/kiotviet.csdl|res://*/kiotviet.ssdl|res://*/kiotviet.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=dc2d-fnb-mssql-01.citigo.io;initial catalog=KiotVietFnB9534;persist security info=True;user id=kiotvietdev;password=********$6162;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="KVEntities1" connectionString="metadata=res://*/kiotviet.csdl|res://*/kiotviet.ssdl|res://*/kiotviet.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=dc2d-fnb-mssql-01.citigo.io;initial catalog=KiotVietFnB9534;persist security info=True;user id=kiotvietdev;password=********$6162;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="KVEntities2" connectionString="metadata=res://*/kiotviet.csdl|res://*/kiotviet.ssdl|res://*/kiotviet.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=dc2d-fnb-mssql-01.citigo.io;initial catalog=KiotVietFnB9534;persist security info=True;user id=kiotvietdev;password=********$6162;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="KVEntities7" connectionString="metadata=res://*/kiotviet.csdl|res://*/kiotviet.ssdl|res://*/kiotviet.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=dc2d-fnb-mssql-01.citigo.io;initial catalog=KiotVietFnB9534;persist security info=True;user id=kiotvietdev;password=********$6162;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
    <add name="KVEntities12" connectionString="metadata=res://*/kiotviet.csdl|res://*/kiotviet.ssdl|res://*/kiotviet.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=dc2d-fnb-mssql-01.citigo.io;initial catalog=KiotVietFnB9534;persist security info=True;user id=kiotvietdev;password=********$6162;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <system.web.webPages.razor>
    <host factoryType="System.Web.Mvc.MvcWebRazorHostFactory, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    <pages pageBaseType="ServiceStack.Razor.ViewPage">
      <namespaces>
        <add namespace="System" />
        <add namespace="System.Linq" />
        <add namespace="ServiceStack" />
        <add namespace="ServiceStack.Html" />
        <add namespace="ServiceStack.Razor" />
        <add namespace="ServiceStack.Text" />
        <add namespace="ServiceStack.OrmLite" />
        <add namespace="KiotViet.FnB.Public.Api" />
        <add namespace="KiotViet.FnB.Public.Api.ServiceModel" />
      </namespaces>
    </pages>
  </system.web.webPages.razor>
</configuration>