﻿using KiotViet.MongoDb.Entity;
using KiotViet.MongoServices.Interface;
using KiotViet.Persistence;
using KiotViet.Persistence.Common;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Resources;
using KiotViet.Services.Common;
using KiotViet.Exceptions;
using KiotViet.Services.Interface;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
//using KiotViet.Services.ElasticSearchModel;
using KiotViet.Web.Api;
using Linq2Rest;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class ProductApi : BaseApi
    {
        public IBranchService BranchService { get; set; }
        public IAttributeService AttributeService { get; set; }
        public IProductService ProductService { get; set; }
        public IProductBranchService ProductBranchService { get; set; }
        public IProductAttributeService ProductAttributeService { get; set; }
        public IProductImageService ProductImageService { get; set; }
        public IProductToppingService ProductToppingService { get; set; }
        public ICategoryService CategoryService { get; set; }
        public IPriceBookService PriceBookService { get; set; }
        public IPriceBookDetailService PriceBookDetailService { get; set; }
        public IStockTakeService StockTakeService { get; set; }
        public IAuditTrailService AuditTrailService { get; set; }
        public IShadowService ShadowService { get; set; }
        public PosSetting Settings { get; set; }
        public IProductFormulaService ProductFormulaService { get; set; }
        public IAuthService AuthService { get; set; }
        private static readonly int RegexTimeOutInSecond = 3;

        public async Task<object> Get(GetListProducts req)
        {
            req.BranchIds = await GetActiveBranchIds(req.BranchIds);

           

            var query = ProductService.GetProducts().AsNoTracking();
            if (req.CategoryId != null)
                query = query.Where(p => p.CategoryId == req.CategoryId);
            if (req.MasterUnitId != null)
                query = query.Where(p => p.MasterUnitId == req.MasterUnitId);

            if (req.LastModifiedFrom != null)
            {
                query = query.Where(r => (r.ModifiedDate == null && r.CreatedDate >= req.LastModifiedFrom) || (r.ModifiedDate != null && r.ModifiedDate >= req.LastModifiedFrom));
            }

            #region IncludeInventory
            var productBranches = new Dictionary<long, IEnumerable<ProductInventoryDTO>>();
            if (req.IncludeInventory)
            {

                productBranches = ProductBranchService.GetProductInventories(req.BranchIds)
                    .GroupBy(x => x.ProductId)
                    .ToDictionary(g => g.Key, g => g.Select(x => new ProductInventoryDTO
                    {
                        ProductId = x.ProductId,
                        BranchId = x.BranchId,
                        BranchName = x.BranchName,
                        Cost = x.Cost,
                        OnHand = x.OnHand,
                        Reserved = x.Reserved
                    }));
 
            }
            #endregion

            #region IncludePriceBook
            var productPriceBooks = new Dictionary<long, IEnumerable<ProductPriceBookDTO>>();
            if (req.IncludePriceBook)
            {
                productPriceBooks = PriceBookService.GetPriceBooks(req.BranchIds).GroupBy(x => x.ProductId).ToDictionary(g => g.Key, g => g.Select(x => new ProductPriceBookDTO
                {
                    ProductId = x.ProductId,
                    PriceBookId = x.PriceBookId,
                    PriceBookName = x.PriceBookName,
                    Price = x.Price,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate,
                    IsActive = x.IsActive
                }));
            }
            #endregion

            #region IncludeImage
            var productImages = ProductImageService.GetImages().GroupBy(x => x.ProductId).ToDictionary(g => g.Key, g => g.Select(x => x.Image));
            #endregion

            #region pagedProducts: Sort, Filter, Paging and Convert to ProductResponseDTO
            var products = (from p in query
                            join c in CategoryService.GetAll() on p.CategoryId equals c.Id into cat
                            from ca in cat.DefaultIfEmpty()
                            select new ProductResponseDTO
                            {
                                Id = p.Id,
                                Code = p.Code,
                                Name = p.Name,
                                FullName = p.FullName,
                                RetailerId = p.RetailerId,
                                CategoryId = p.CategoryId,
                                CategoryName = ca.Name,
                                MasterUnitId = p.MasterUnitId,
                                MasterProductId = p.MasterProductId,
                                ConversionValue = p.ConversionValue,
                                AllowsSale = p.AllowsSale,
                                HasVariants = p.HasVariants,
                                BasePrice = p.BasePrice,
                                Unit = p.Unit,
                                Weight = p.Weight,
                                Description = p.Description,
                                ModifiedDate = p.ModifiedDate,
                                //isActive = p.isActive,
                                OrderTemplate = p.OrderTemplate,
                                ProductType = p.ProductType,
                                IsTopping = p.IsTopping,
                                IsProcessedGoods = p.IsProcessedGoods,
                                IsRewardPoint = p.IsRewardPoint,
                                IsTimeType = p.IsTimeType,
                                CreatedDate = p.CreatedDate,
                                IsTimeServices = p.IsTimeServices
                            });

            if (req.Orderby == null) products = products.OrderBy(p => p.Name);

            var filteredProducts = products.Filter(req.GetModelFilter());
            var pagedProducts = (await filteredProducts.Take(req).ToListAsync()).Select(x => x.ConvertTo<ProductResponseDTO>()).ToList();
            var pagedProductIDs = pagedProducts.Select(p => p.Id).ToList();
            #endregion

            #region Product Attributes
            var productsWithAttributes = ProductAttributeService.GetAttributes().Where(x => pagedProductIDs.Contains(x.ProductId))
                .GroupBy(x => x.ProductId).ToDictionary(g => g.Key, g => g.Select(x => new ProductAttributeDTO
            {
                ProductId = x.ProductId,
                AttributeName = x.AttributeName,
                AttributeValue = x.AttributeValue
            }));
            #endregion

            #region Product Topping
            var productToppings = ProductToppingService.GetByProductIdsAsync(pagedProductIDs)
                .GroupBy(pt => pt.ProductId).ToDictionary(g => g.Key, g => g.Select(pt => new ProductDTO
               {
                   Id = pt.ToppingId,
                   RetailerId = pt.Topping.RetailerId,
                   Code = pt.Topping.Code,
                   Name = pt.Topping.Name,
                   FullName = pt.Topping.FullName,                  
                   CategoryId = pt.Topping.CategoryId,
                   BasePrice = pt.Topping.BasePrice
               }));
            #endregion

            #region Product Formula
            var productFormulas = ProductFormulaService.GetByProductIdsAsync(pagedProductIDs)
                .GroupBy(pt => pt.ProductId).ToDictionary(g => g.Key, g => g.Select(pf => new ProductFormulaDTO
                {
                    Id = pf.MaterialId,
                    RetailerId = pf.Material.RetailerId,
                    Code = pf.Material.Code,
                    Name = pf.Material.Name,
                    FullName = pf.Material.FullName,
                    CategoryId = pf.Material.CategoryId,
                    BasePrice = pf.Material.BasePrice,
                    Quantity = pf.Quantity
                }));
            #endregion

            #region Product Units
            var productUnits = await query.Where(p => pagedProductIDs.Contains((long)p.MasterUnitId))
                .GroupBy(p => p.MasterUnitId).ToDictionaryAsync(g => g.Key, g => g.Select(p => new ProductUnitDTO
            {
                Id = p.Id,
                Code = p.Code,
                Name = p.Name,
                FullName = p.FullName,
                Unit = p.Unit,
                ConversionValue = p.ConversionValue,
                BasePrice = p.BasePrice
            }).ToList());
            #endregion

            #region Build Data Source from pagedProducts
            pagedProducts.ForEach(x =>
            {
                x.Attributes = productsWithAttributes.ContainsKey(x.Id) ? productsWithAttributes[x.Id] : null;
                x.Toppings = productToppings.ContainsKey(x.Id) ? productToppings[x.Id] : null;
                x.Formulas = productFormulas.ContainsKey(x.Id) ? productFormulas[x.Id] : null;
                x.Units = productUnits.ContainsKey(x.Id) ? productUnits[x.Id] : null;
                x.Inventories = productBranches.ContainsKey(x.Id)
                    ? productBranches[x.Id]
                        .Select(p => new ProductInventoryDTO
                        {
                            ProductId = p.ProductId,
                            ProductCode = x.Code,
                            ProductName = x.Name,
                            BranchId = p.BranchId,
                            BranchName = p.BranchName,
                            Cost = p.Cost,
                            OnHand = p.OnHand,
                            Reserved = p.Reserved
                        })
                    : null;
                x.PriceBooks = productPriceBooks.ContainsKey(x.Id) ? productPriceBooks[x.Id] : null;
                x.Images = productImages.ContainsKey(x.Id) ? productImages[x.Id] : null;
            });

            var ds = new SyncDataSources<ProductResponseDTO>();
            ds.Data = pagedProducts;
            ds.PageSize = GetPageSize(req.PageSize);
            ds.Total = await products.CountAsync();
            #endregion

            #region Include RemovedIDs
            if (req.IncludeRemoveIds)
            {
                var del = await ShadowService.GetDeletedProductAsync(CurrentRetailerId, req.LastModifiedFrom);
                List<long> delSoft;
                if (req.LastModifiedFrom == null || req.LastModifiedFrom.Value == default(DateTime))
                {
                    delSoft = await ProductService.GetAll()
                        .Where(p => p.isDeleted != null && p.isDeleted.Value)
                        .Select(v => v.Id)
                        .ToListAsync();
                }
                else
                {
                    delSoft = await ProductService.GetAll()
                        .Where(p => p.isDeleted != null && p.isDeleted.Value &&
                                    (p.CreatedDate >= req.LastModifiedFrom.Value ||
                                     (p.ModifiedDate != null && p.ModifiedDate.Value >= req.LastModifiedFrom.Value)))
                        .Select(v => v.Id)
                        .ToListAsync();
                }
                ds.RemovedIds = del.Concat(delSoft).Distinct().ToList();
            }
            #endregion

            return await Request.ToOptimizedResultAsync(ds);
        }

        public async Task<ProductResponseDTO> Get(GetProduct req)
        {
            Product exist;
            if (req.Id > 0)
            {
                exist = await ProductService.GetByIdAsync(req.Id);
            }
            else
            {
                if (string.IsNullOrEmpty(req.Code))
                    throw new KvException("Mã hàng hóa không hợp lệ");
                exist = await ProductService.GetAll().FirstOrDefaultAsync(p => p.Code == req.Code.Trim());
            }

            req.BranchIds = await GetActiveBranchIds(req.BranchIds);

            if (exist == null || (exist.isDeleted ?? false)) throw new KvException(KVMessage.product_NotFound);

            var product = exist.ConvertTo<ProductResponseDTO>();
            product.CategoryName = exist.Category?.Name;

            #region Product Units
            var productUnits = await ProductService.GetProducts().Where(x => x.isActive && x.MasterUnitId == product.Id).Select(x => new ProductUnitDTO
            {
                Id = x.Id,
                Code = x.Code,
                Name = x.Name,
                FullName = x.FullName,
                Unit = x.Unit,
                ConversionValue = x.ConversionValue,
                BasePrice = x.BasePrice
            }).ToListAsync();
            product.Units = productUnits.Count > 0 ? productUnits : null;
            #endregion

            #region Attributes
            if (exist.ProductAttributes.Any())
            {
                var attributes = ProductAttributeService.DetachByClone(exist.ProductAttributes.ToList(), new string[] { "Attribute" });
                product.Attributes = attributes.Select(x => new ProductAttributeDTO
                {
                    ProductId = x.ProductId,
                    AttributeName = x.Attribute?.Name,
                    AttributeValue = x.Value
                });
            }
            #endregion

            #region Inventories

            product.Inventories = ProductBranchService.GetProductInventories(req.BranchIds, product.Id)
                .Select(x => new ProductInventoryDTO
                {
                    ProductId = x.ProductId,
                    BranchId = x.BranchId,
                    BranchName = x.BranchName,
                    Cost = x.Cost,
                    OnHand = x.OnHand,
                    Reserved = x.Reserved
                });
            #endregion

            #region Toppings
            var toppings = await ProductToppingService.GetAll().Where(x => x.ProductId == product.Id).Select(pt => new ProductDTO
            {
                Id = pt.ToppingId,
                RetailerId = pt.Topping.RetailerId,
                Code = pt.Topping.Code,
                Name = pt.Topping.Name,
                FullName = pt.Topping.FullName,
                CategoryId = pt.Topping.CategoryId,
                BasePrice = pt.Topping.BasePrice
            }).ToListAsync();
            product.Toppings = toppings.Count > 0 ? toppings : null;
            #endregion

            #region Formulas
            var formulas = await ProductFormulaService.GetAll().Where(x => x.ProductId == product.Id).Select(pf => new ProductFormulaDTO
            {
                Id = pf.MaterialId,
                RetailerId = pf.Material.RetailerId,
                Code = pf.Material.Code,
                Name = pf.Material.Name,
                FullName = pf.Material.FullName,
                CategoryId = pf.Material.CategoryId,
                BasePrice = pf.Material.BasePrice,
                Quantity = pf.Quantity
            }).ToListAsync();
            product.Formulas = formulas.Count > 0 ? formulas : null;
            #endregion

            #region PriceBook
            if (exist.PriceBookDetails.Any())
            {
                var productPriceBooks = PriceBookDetailService.DetachByClone(exist.PriceBookDetails.ToList(), new string[] { "PriceBook" });
                product.PriceBooks = productPriceBooks.Select(x => new ProductPriceBookDTO
                {
                    ProductId = x.ProductId,
                    PriceBookId = x.PriceBookId,
                    PriceBookName = x.PriceBook?.Name,
                    Price = x.Price,
                    StartDate = x.PriceBook?.StartDate,
                    EndDate = x.PriceBook?.EndDate,
                    IsActive = x.PriceBook?.IsActive ?? false
                });
            }
            #endregion

            #region Images
            var productImages = await ProductImageService.GetByProductId(exist.Id).Select(a => a.Image).ToListAsync();
            if (productImages.Any())
            {
                product.Images = productImages;
            }
            #endregion

            return product;
        }

        private async Task<List<int>> GetActiveBranchIds(List<int> branchIds)
        {
            if (branchIds == null || !branchIds.Any())
            {
                return await BranchService.GetByRetailer(CurrentRetailerId).Select(branch => branch.Id).ToListAsync();
            }
            return branchIds ?? new List<int>();
        }

        public async Task<ProductDTO> Post(CreateProduct req)
        {
            var product = req;
            product.Id = 0;
            var isMasterUnit = !product.MasterUnitId.HasValue || product.MasterUnitId == 0;
            var category = await GetCategory(product);
            var allCategories = await CategoryService.GetAll().ToListAsync();
            product.CategoryId = category.Id > 0 ? category.Id : product.CategoryId;
            await ValidateProduct(product);
            if (product.MasterProductId != null)
            {
                var isMasterProduct = await ProductService.GetAll().AnyAsync(p => p.Id == product.MasterProductId && p.MasterProductId == null);
                var masterProductAttrName = await ProductAttributeService.GetAttributesByProductIdAsync(product.MasterProductId ?? 0)
                    .Select(p => p.Name)
                    .ToListAsync();
                ValidateMasterProductId(isMasterProduct, masterProductAttrName, product.Attributes, product.HasVariants, true);
            }
            var newProduct = new Product
            {
                Code = !string.IsNullOrEmpty(product.Code) ? Regex.Replace(product.Code, @"\s+", " ", RegexOptions.None, TimeSpan.FromSeconds(RegexTimeOutInSecond)) : string.Empty,
                Name = Regex.Replace(product.Name, @"\s+", " ", RegexOptions.None, TimeSpan.FromSeconds(RegexTimeOutInSecond)),
                ProductType = (byte)ProductType.Purchased,
                CategoryId = category.Id > 0 ? category.Id : product.CategoryId,
                AllowsSale = product.AllowsSale != null && product.AllowsSale.Value,
                HasVariants = product.Attributes != null && product.Attributes.Any(),
                BasePrice = product.BasePrice.HasValue
                    ? (product.BasePrice != null && product.BasePrice.Value > 0 ? product.BasePrice.Value : 0)
                    : 0,
                Weight = product.Weight.HasValue
                    ? (product.Weight != null && product.Weight.Value > 0 ? product.Weight.Value : 0)
                    : 0,
                Unit = !string.IsNullOrEmpty(product.Unit) ? product.Unit.Trim() : null,
                MasterUnitId = product.MasterUnitId,
                MasterProductId = product.MasterProductId,
                ConversionValue = product.MasterUnitId == null ? 1 : (product.ConversionValue ?? 0),
                Description = product.Description
            };

            var result = await ProductService.CreateOrUpdateAsync(newProduct, isMasterUnit);
            await CreateAttributesForProduct(product, result);

            // update product fullname
            await ProductService.UpdateFullNameAsync(result.Id);

            #region Log

            var log = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                //BranchId = req.Inventories != null && req.Inventories.Any() ? req.Inventories.FirstOrDefault().BranchId : (await BranchService.GetDefaultBranchAsync()).Id
            };
            var categoryStr = GetFullPathNameCategory(result.CategoryId, allCategories);
            log.Action = (int)AuditTrailAction.Create;
            var productUnit = !string.IsNullOrEmpty(result.Unit) ? $"({result.Unit})" : "";
            log.Content = $"[PublicAPI] Thêm mới sản phẩm: [ProductCode]{result.Code}[/ProductCode], tên: {result.Name}{productUnit}, nhóm hàng: {categoryStr}, giá bán: {NormallizeWfp((double)result.BasePrice)}, được bán trực tiếp: {(result.AllowsSale ? "Có" : "Không")}";
            var rs = await AuditTrailService.AddLog(log);

            #endregion

            await CreateInventoryForProduct(product, result, rs, false);

            if (product.Images != null && product.Images.Any())
            {
                var productImages = product.Images.Select(img => Tuple.Create(result.Id, img)).ToList();
                await ProductImageService.BatchAddProductImageAsync(productImages);

            }
            await ProductBranchService.UpdateManufacturedCostByMaterialAsync(result.Id);

            //ProductService.SendToEsAddOrChange(new ProductAddModel { ProductIds = new[] { result.Id }, RetailerId = AuthService.Context.RetailerId, Zone = AuthService.Context.Group.Id });

            // return ProductDTO
            return await Get(new GetProduct { Id = result.Id });
        }

        private static void ValidateMasterProductId(bool isMasterProduct, List<string> masterProductAttrName, IEnumerable<ProductAttributeDTO> attributes, bool? hasVariants, bool isCreate)
        {
            if (!isMasterProduct)
                throw new KvValidateException("masterProductId không hợp lệ");

            if (isCreate && (attributes == null || (masterProductAttrName == null) || (masterProductAttrName != null && !masterProductAttrName.Any())))
                throw new KvValidateException("Hàng hoá cùng loại chưa có thuộc tính");
            if (isCreate && (hasVariants == null || (hasVariants != null && hasVariants.Value == false)))
                throw new KvValidateException("Không thể thêm thuộc tính của hàng hoá cùng loại vì hasVariants != true");
            if (attributes != null && masterProductAttrName != null && (!attributes.All(p => masterProductAttrName.Contains(p.AttributeName))
                || attributes.Count() != masterProductAttrName.Count))
                throw new KvValidateException("Danh sách thuộc tính hàng hoá không hợp lệ");
        }


        private async Task ValidateProduct(ProductDTO product)
        {
            var branches = await BranchService.GetByRetailer(CurrentRetailerId).ToListAsync();

            // validate productCode
            if (product.Code != null && product.Code.Length > 50) throw new KvValidateException(KVMessage.product_CodeMaxLength);

            // validate productName
            if (string.IsNullOrEmpty(product.Name) || string.IsNullOrWhiteSpace(product.Name)) throw new KvValidateException("Tên sản phẩm không hợp lệ");

            // validate product category
            //if (product.CategoryId > 0 && allCategories != null)
            //{
            //    var existedCategory = allCategories.FirstOrDefault(x => x.Id == product.CategoryId);
            //    if (existedCategory == null || (existedCategory.isDeleted ?? false)) throw new KvValidateException(KVMessage.category_NotFound);
            //}

            // validate inventories
            if (product.Inventories != null && product.Inventories.Any())
            {
                foreach (var inventory in product.Inventories)
                {
                    var branch = branches.FirstOrDefault(x => x.Id == inventory.BranchId);
                    if (branch == null) throw new KvValidateException(KVMessage.MsgBranchIdEmpty);
                    if (branch.LimitAccess == true) throw new KvValidateException($"Chi nhánh {branch.Name} đã ngừng hoạt động");
                    if (inventory.Cost.GetValueOrDefault(0) < 0) throw new KvValidateException(Labels.files_NotPriceBase);
                    if (inventory.OnHand.GetValueOrDefault(1) < 0) throw new KvValidateException(Labels.files_NotStocktake);
                }
            }

            // validate attributes
            if (product.Attributes != null && product.Attributes.Any())
            {
                foreach (var productAttribute in product.Attributes)
                {
                    if (string.IsNullOrEmpty(productAttribute.AttributeName) || string.IsNullOrWhiteSpace(productAttribute.AttributeName)) throw new KvValidateException("Tên thuộc tính không hợp lệ");
                    if (string.IsNullOrEmpty(productAttribute.AttributeValue) || string.IsNullOrWhiteSpace(productAttribute.AttributeValue)) throw new KvValidateException("Giá trị thuộc tính không hợp lệ");
                }
            }

            // validate images
            if (product.Images != null && product.Images.Any(productImage => string.IsNullOrEmpty(productImage) || string.IsNullOrWhiteSpace(productImage)))
            {
                throw new KvValidateException("Ảnh sản phẩm không hợp lệ");
            }

            // hiện tại chưa hỗ trợ thêm sản phẩm có đơn vị quy đổi
            // validate units
            //if (product.Units != null && product.Units.Any())
            //{
            //    foreach (var productUnit in product.Units)
            //    {
            //        if (string.IsNullOrEmpty(productUnit.Unit) || string.IsNullOrWhiteSpace(productUnit.Unit)) throw new KvValidateException("Tên đơn vị quy đổi không hợp lệ");
            //        if (Math.Abs(productUnit.ConversionValue) < 0.001 || productUnit.ConversionValue  == 0) throw new KvValidateException("Giá trị quy đổi cho đơn vị tính không hợp lệ");
            //    }
            //}
        }

        public async Task<ProductDTO> Put(UpdateProduct req)
        {
            var product = req;
            var existingProduct = await ProductService.GetAll().FirstOrDefaultAsync(x => x.Id == req.Id || x.Code.Trim() == product.Code);
            if (existingProduct == null || (existingProduct.isDeleted ?? false)) throw new KvException(KVMessage.product_NotFound);
            var oldProduct = existingProduct.ConvertTo<Product>();
            var category = await GetCategory(product);
            var allCategories = await CategoryService.GetAll().ToListAsync();
            product.CategoryId = category.Id > 0 ? category.Id : oldProduct.CategoryId;
            await ValidateProduct(product);
            if (existingProduct.IsLotSerialControl.GetValueOrDefault() || existingProduct.ProductType != (byte)ProductType.Purchased)
            {
                throw new KvValidateException($"Hàng hóa {existingProduct.Code} không thể cập nhật do API chưa hỗ trợ loại hàng hóa này");
            }
            if (existingProduct.MasterProductId != null)
            {
                var isMasterProduct = await ProductService.GetAll().AnyAsync(p => p.Id == existingProduct.MasterProductId && p.MasterProductId == null);
                var masterProductAttrName = await ProductAttributeService.GetAttributesByProductIdAsync(existingProduct.MasterProductId ?? 0)
                    .Select(p => p.Name)
                    .ToListAsync();
                ValidateMasterProductId(isMasterProduct, masterProductAttrName, product.Attributes, product.HasVariants, false);
            }
            var oldCategoryName = GetFullPathNameCategory(existingProduct.CategoryId, allCategories);
            var result = await UpdateProduct(existingProduct, product);

            await CreateAttributesForProduct(product, result);
            // update product fullname
            await ProductService.UpdateFullNameAsync(result.Id);

            #region Log

            var log = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                Action = (int)AuditTrailAction.Update,
                //BranchId = (await BranchService.GetDefaultBranchAsync()).Id
            };

            var newCategoryName = string.Empty;
            if (oldProduct.CategoryId != existingProduct.CategoryId) newCategoryName = GetFullPathNameCategory(existingProduct.CategoryId, allCategories);
            var code = !string.IsNullOrEmpty(product.Code) && !existingProduct.Code.Equals(oldProduct.Code, StringComparison.Ordinal) ? $" {oldProduct.Code}->[ProductCode]{existingProduct.Code}[/ProductCode]" : $"[ProductCode]{oldProduct.Code}[/ProductCode]";
            var name = !string.IsNullOrEmpty(product.Name) && !existingProduct.Name.Equals(oldProduct.Name, StringComparison.Ordinal) ? $", tên: {oldProduct.Name}->{existingProduct.Name}" : string.Empty;
            var basePrice = product.BasePrice != null && oldProduct.BasePrice != existingProduct.BasePrice ? $", giá bán: {NormallizeWfp((double)oldProduct.BasePrice)}->{NormallizeWfp((double)existingProduct.BasePrice)}" : string.Empty;
            var unit = !string.IsNullOrEmpty(product.Unit) && !existingProduct.Unit.Equals(oldProduct.Unit, StringComparison.Ordinal) ? $", đơn vị: {oldProduct.Unit}->{existingProduct.Unit}" : string.Empty;
            var logCategory = oldProduct.CategoryId != existingProduct.CategoryId ? $", nhóm hàng: {oldCategoryName} thành {newCategoryName}" : $", nhóm hàng: {oldCategoryName}";
            log.Content = $"[PublicAPI] Cập nhật thông tin sản phẩm: {code}{name}{logCategory}{unit}{basePrice}";
            var rs = await AuditTrailService.AddLog(log);

            #endregion

            await CreateInventoryForProduct(product, result, rs, true);

            if (product.Images != null && product.Images.Any())
            {
                var productImages = product.Images.Select(img => Tuple.Create(result.Id, img)).ToList();
                await ProductImageService.BatchAddProductImageAsync(productImages);

            }
            await ProductBranchService.UpdateManufacturedCostByMaterialAsync(result.Id);

            //ProductService.SendToEsAddOrChange(new ProductAddModel { ProductIds = new[] { result.Id }, RetailerId = AuthService.Context.RetailerId, Zone = AuthService.Context.Group.Id });

            return await Get(new GetProduct { Id = result.Id });
        }

        private async Task<Product> UpdateProduct(Product oldProduct, UpdateProduct newProduct)
        {
            var copyProduct = new Product();
            copyProduct.CopyFrom(oldProduct);

            copyProduct.AllowsSale = newProduct.AllowsSale ?? oldProduct.AllowsSale;
            copyProduct.HasVariants = newProduct.Attributes?.Any() ?? oldProduct.HasVariants;
            copyProduct.Code = !string.IsNullOrEmpty(newProduct.Code) ? Regex.Replace(newProduct.Code, @"\s+", " ", RegexOptions.None, TimeSpan.FromSeconds(RegexTimeOutInSecond)) : oldProduct.Code;
            copyProduct.Name = !string.IsNullOrEmpty(newProduct.Name) ? Regex.Replace(newProduct.Name, @"\s+", " ", RegexOptions.None, TimeSpan.FromSeconds(RegexTimeOutInSecond)) : oldProduct.Name;
            copyProduct.CategoryId = newProduct.CategoryId > 0 ? newProduct.CategoryId : oldProduct.CategoryId;
            copyProduct.BasePrice = newProduct.BasePrice.HasValue ? (newProduct.BasePrice.Value > 0 ? newProduct.BasePrice.Value : 0) : oldProduct.BasePrice;
            copyProduct.Weight = newProduct.Weight.HasValue ? (newProduct.Weight.Value > 0 ? newProduct.Weight.Value : 0) : oldProduct.Weight;
            copyProduct.Unit = !string.IsNullOrEmpty(newProduct.Unit) ? Regex.Replace(newProduct.Unit.Trim(), @"\s+", " ", RegexOptions.None, TimeSpan.FromSeconds(RegexTimeOutInSecond)) : oldProduct.Unit;
            //copyProduct.MasterUnitId = newProduct.MasterUnitId != null && newProduct.MasterUnitId > 0 ? newProduct.MasterUnitId : oldProduct.MasterUnitId;
            copyProduct.ConversionValue = newProduct.ConversionValue.HasValue ? (newProduct.ConversionValue.Value > 0 ? newProduct.ConversionValue.Value : 0) : oldProduct.ConversionValue;
            copyProduct.Description = !string.IsNullOrEmpty(newProduct.Description) ? newProduct.Description : oldProduct.Description;
            //copyProduct.isActive = newProduct.isActive ?? oldProduct.isActive;
            //copyProduct.IsRewardPoint = newProduct.IsRewardPoint ?? oldProduct.IsRewardPoint;

            var isMasterUnit = !copyProduct.MasterUnitId.HasValue || copyProduct.MasterUnitId == 0;
            var result = await ProductService.CreateOrUpdateAsync(copyProduct, isMasterUnit);
            return result;
        }

        private static string GetFullPathNameCategory(int catId, List<Category> allCategories)
        {
            var content = string.Empty;
            if (catId > 0)
            {
                var cat = allCategories.Select(i => new { i.ParentId, i.Id, i.Name }).ToList();
                var cateroryOfProduct = cat.FirstOrDefault(x => x.Id == catId);
                if (cateroryOfProduct?.Name != null)
                {
                    content = cateroryOfProduct.Name;
                    if (cateroryOfProduct.ParentId != null && cateroryOfProduct.ParentId > 0)
                    {
                        var checkParent = true;
                        var categoryId = cateroryOfProduct.ParentId;
                        var checkLevelMax = 0;
                        while (checkParent && checkLevelMax < 10)
                        {
                            checkLevelMax += 1;
                            var parent = cat.FirstOrDefault(x => x.Id == categoryId);
                            if (parent != null)
                            {
                                if (parent.Name != null)
                                {
                                    content = parent.Name + ">>" + content;
                                }
                                if (parent.ParentId == null || parent.ParentId <= 0)
                                {
                                    checkParent = false;
                                }
                                else
                                {
                                    categoryId = parent.ParentId;
                                }
                            }
                            else
                            {
                                checkParent = false;
                            }
                        }
                    }
                }
            }
            return content;
        }

        private async Task CreateInventoryForProduct(ProductDTO product, Product result, bool rs, bool isUpdate)
        {
            if (product.Inventories != null && product.Inventories.Any())
            {
                var lsProductBranchsExisted = await ProductBranchService.GetAll().Where(x => x.ProductId == result.Id).ToDictionaryAsync(k => k.BranchId);
                foreach (var inventory in product.Inventories)
                {
                    lsProductBranchsExisted.TryGetValue(inventory.BranchId, out ProductBranch existedProductBranch);
                    if (!Settings.UseAvgCost || !isUpdate)
                    {
                        var newCost = inventory.Cost ?? 0;
                        var oldCost = existedProductBranch?.Cost ?? 0;

                        if (!isUpdate || oldCost != newCost)
                        {
                            await ProductService.ChangeCostAsync(new CostTracking
                            {
                                NewCost = newCost,
                                BranchId = inventory.BranchId,
                                RetailerId = result.RetailerId,
                                ProductId = result.Id
                            });

                            var productUpdatedCost = await ProductBranchService.UpdateOrCreateCostAsync(result.Id, inventory.BranchId, newCost);

                            await WriteLogForCostChange(result.Code, oldCost, productUpdatedCost.Cost, inventory.BranchId, isUpdate);
                        }
                    }

                    var isadd = !isUpdate ? 1 : 0;
                    if (inventory.OnHand != null)
                    {
                        var newOndHand = Math.Round(inventory.OnHand > 0 ? inventory.OnHand.Value : 0, 3);
                        var oldOnHand = Math.Round(existedProductBranch?.OnHand ?? 0, 3);
                        if (!isUpdate || Math.Abs(newOndHand - oldOnHand) > KVConst.Tolerance)
                        {
                            var stock = (StockTake)await StockTakeService.CreateStockTakeForUpdateProduct(result.Id, result.Code, newOndHand, inventory.BranchId, result.RetailerId, isadd);

                            #region Log

                            var productDetail = "";
                            if (stock.StockTakeDetails.Count > 0)
                            {
                                productDetail = ", bao gồm:<div>";
                                foreach (var item in stock.StockTakeDetails)
                                {
                                    productDetail += $"- [ProductCode]{result.Code}[/ProductCode] : {Normallize(item.ActualCount)}/{Normallize(item.SystemCount)}<br>";
                                }
                                productDetail += "</div>";
                            }
                            var logStock = new AuditTrailLog
                            {
                                FunctionId = (int)FunctionType.StockTake,
                                Action = (int)AuditTrailAction.Create,
                                Content = $"Tạo phiếu kiểm kho: [StockTakeCode]{stock.Code}[/StockTakeCode], ngày cân bằng kho: {DateFormat(stock.AdjustmentDate ?? default(DateTime))}{productDetail}",
                                BranchId = inventory.BranchId
                            };

                            if (rs)
                            {
                                await AuditTrailService.AddLog(logStock);
                            }

                            #endregion

                            await ProductBranchService.UpdateOrCreateOnHandAsync(result.Id, inventory.BranchId, newOndHand);
                        }
                    }
                }
            }
        }

        private async Task WriteLogForCostChange(string productCode, decimal oldCost, decimal newCost, int branchId, bool isUpdate)
        {
            var logContent = isUpdate
                ? $"Giá vốn được cập nhật (khi cập nhật sản phẩm [ProductCode]{productCode}[/ProductCode]): {NormallizeWfp((double)oldCost)}->{NormallizeWfp((double)newCost)}"
                : $"Giá vốn được cập nhật (khi thêm mới sản phẩm [ProductCode]{productCode}[/ProductCode]): {NormallizeWfp((double)newCost)}";
            var logStock = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                Action = (int)AuditTrailAction.Update,
                Content = logContent,
                BranchId = branchId
            };
            await AuditTrailService.AddLog(logStock);
        }

        private async Task CreateAttributesForProduct(ProductDTO product, Product result)
        {
            if (product.Attributes != null)
            {
                // create or update attributes for product
                var productAttributes = new List<ProductAttribute>();
                if (product.Attributes.Any())
                {
                    var allAttributes = await AttributeService.GetAll().ToListAsync();
                    foreach (var productAttr in product.Attributes)
                    {
                        var attr = allAttributes.FirstOrDefault(x => x.Name.Trim().ToLower().Equals(productAttr.AttributeName.Trim().ToLower()));
                        if (attr != null)
                        {
                            var existProductAttribute = new ProductAttribute
                            {
                                ProductId = result.Id,
                                Product = result,
                                AttributeId = attr.Id,
                                Attribute = attr,
                                Value = productAttr.AttributeValue
                            };
                            productAttributes.Add(existProductAttribute);
                        }
                        else
                        {
                            var newAttribute = (Persistence.Attribute)await AttributeService.CreateOrUpdateAsync(new Persistence.Attribute
                            {
                                Name = productAttr.AttributeName
                            });
                            productAttributes.Add(new ProductAttribute
                            {
                                ProductId = result.Id,
                                AttributeId = newAttribute.Id,
                                Value = productAttr.AttributeValue
                            });
                        }
                    }
                }
                await ProductAttributeService.CreateOrUpdateAsync(productAttributes, result.Id, result.HasVariants);
            }
        }

        public async Task<object> Delete(DeleteProduct req)
        {
            var lsDeleted = await ProductService.DeleteAsync(req.Id);
            if (lsDeleted == null || !lsDeleted.Any()) throw new KvValidateException(KVMessage._GlobalDeleteError);

            #region Log

            var content = new StringBuilder();
            if (lsDeleted.Count == 1)
            {
                if (!lsDeleted[0].Item4) throw new KvValidateException(lsDeleted[0].Item5);
                content.AppendFormat($"[PublicAPI] Xóa thông tin hàng hóa mã: {lsDeleted[0].Item2}, tên: {lsDeleted[0].Item3}");
            }
            else if (lsDeleted.Count > 1)
            {
                content.AppendFormat("[PublicAPI] Xóa thông tin hàng hóa mã:<br><div>");
                foreach (var item in lsDeleted.Where(x => x.Item4))
                {
                    content.AppendFormat($" - {item.Item2}, tên: {item.Item3}");
                    content.AppendFormat("<br>");
                }
                content.AppendFormat("</div>");
            }
            var log = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Product,
                Action = (int)AuditTrailAction.Delete,
                Content = content.ToString(),
                //BranchId = (await BranchService.GetDefaultBranchAsync()).Id
            };
            await AuditTrailService.AddLog(log);

            #endregion


            //ProductService.SendToEsDelete(new ProductDeleteModel { Ids = new long[] { req.Id }, Zone = AuthService.Context.Group.Id, RetailerId = AuthService.Context.RetailerId });

            return new { Message = KVMessage._GlobalDeleteSuccess };
        }
        private async Task<Category> GetCategory(ProductDTO product)
        {
            try
            {
                var oldCategory = new Category();
                if (!string.IsNullOrEmpty(product.CategoryName))
                {
                    oldCategory = await CategoryService.GetAll()
                        .FirstOrDefaultAsync(
                            x => x.Name.Equals(product.CategoryName));
                }
                else if (product.CategoryId > 0)
                {
                    oldCategory = await CategoryService.GetAll()
                        .FirstOrDefaultAsync(
                            x => x.Id == product.CategoryId);
                }
                if (oldCategory != null)
                {
                    return oldCategory;
                }
                var category = new Category { Id = 0, Name = product.CategoryName, IsActive = true };
                var newCategory = await CategoryService.CreateOrUpdateAsync(category);
                return newCategory;
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message, ex);
                throw new KvException(ex.Message);
            }
        }
    }
}
