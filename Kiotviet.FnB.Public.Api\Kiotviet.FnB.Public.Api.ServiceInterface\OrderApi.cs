using KiotViet.Persistence.Common;
using KiotViet.FnB.Public.Api.ServiceModel;
using KiotViet.Services.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Data.Entity;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using KiotViet.Persistence;
using KiotViet.Utilities;
using KiotViet.Resources;
using KiotViet.Exceptions;
using KiotViet.MongoDb.Entity;
using KiotViet.MongoServices.Interface;
using KiotViet.Services.Common;
using Linq2Rest;
using KiotViet.Web.Api;
using System.Data.SqlTypes;
using KiotViet.Api.ServiceModel;
using LinqKit;
using Newtonsoft.Json;
using KiotViet.FirebaseRealTimeDatabase.Interface;
using System.Dynamic;
using KiotViet.FnB.Public.Api.ServiceInterface.Filters;
using KiotViet.CommandDispatcher.Abstractions;
using ServiceStack.Web;
using KiotViet.CommandDispatcher.OrderCommand;
using ServiceStack.Auth;
using ServiceStack.Configuration;
using KiotViet.Auth;
using KiotViet.CommandDispatcher.PublicApiSubmitOrderCommand;

namespace KiotViet.FnB.Public.Api.ServiceInterface
{
    public class OrderApi : BaseApi
    {
        public IOrderService OrderService { get; set; }
        public IOrderDetailService OrderDetailService { get; set; }
        public IInvoiceDeliveryService InvoiceDeliveryService { get; set; }
        public IProductService ProductService { get; set; }
        public IBranchService BranchService { get; set; }
        public IUserService UserService { get; set; }
        public ICustomerService CustomerService { get; set; }
        public IAuditTrailService AuditTrailService { get; set; }
        public ILocationService LocationService { get; set; }
        public IBankAccountService BankAccountService { get; set; }
        public IPaymentService PaymentService { get; set; }
        public IPartnerDeliveryService PartnerDeliveryService { get; set; }
        public IInvoiceService InvoiceService { get; set; }
        public IEventMessageService EventMessageService { get; set; }        
        public ISurchargeService SurchargeService { get; set; }
        public IInvoiceOrderSurchargeService InvoiceOrderSurchargeService { get; set; }
        public ITableAndRoomService TableAndRoomService { get; set; }
        public IProductAttributeService ProductAttributeService { get; set; }
        public IAttributeService AttributeService { get; set; }
        public IBalanceTrackingService BalanceTrackingService { get; set; }
        public IAuthService AuthService { get; set; }
        public ICustomerGroupService CustomerGroupService { get; set; }
        public ITableGroupService TableGroupService { get; set; }       
        public IReservationService ReservationService { get; set; }
        public IReservationTableService ReservationTableService { get; set; }

        public IReservationRealTimeService ReservationRealTimeService { get; set; }
        public IPrinterRealTimeService PrinterRealTimeService { get; set; }
        public IOrderRealTimeService OrderRealTimeService { get; set; }

        public ICancelDishReasonService CancelDishReasonService { get; set; }
        public IDeliveryInfoService DeliveryInfoService { get; set; }
        public ICommandDispatcher CommandDispatcher { get; set; }
        public IAppSettings AppSettings { get; set; }

        private const string TextOfCheckBeforePurchase = "Kiểm đồ trước thanh toán";

        public PosSetting Setting { get; set; }


        private InvoiceDelivery ValidateDeliveryDetail(InvoiceDelivery deliveryDto)
        {

            var returnValue = deliveryDto.ConvertTo<InvoiceDelivery>();
            if (returnValue == null) return returnValue;
            returnValue.Height = returnValue.Height.HasValue ? (returnValue.Height.Value > 0 ? returnValue.Height.Value : 0) : (double?)null;
            returnValue.Length = returnValue.Length.HasValue ? (returnValue.Length.Value > 0 ? returnValue.Length.Value : 0) : (double?)null;
            returnValue.Weight = returnValue.Weight.HasValue ? (returnValue.Weight.Value > 0 ? returnValue.Weight.Value : 0) : (double?)null;
            returnValue.Price = returnValue.Price.HasValue ? (returnValue.Price.Value > 0 ? returnValue.Price.Value : 0) : (decimal?)null;
            returnValue.PriceCodPayment = returnValue.PriceCodPayment.HasValue ? (returnValue.PriceCodPayment.Value > 0 ? returnValue.PriceCodPayment.Value : 0) : (decimal?)null;
            returnValue.Width = returnValue.Width.HasValue ? (returnValue.Width.Value > 0 ? returnValue.Width.Value : 0) : (double?)null;
            return returnValue;
        }
        private ICollection<OrderDetail> ValidateOrderDetail(List<OrderDetail> orderDetail)
        {
            var result = orderDetail;
            foreach (var item in orderDetail)
            {
                item.Uuid = Guid.NewGuid().ToString();
                item.Price = item.Price > 0 ? item.Price : 0;
                item.Discount = item.Discount > 0 ? item.Discount : 0;
                item.OnHand = item.OnHand.HasValue ? (item.OnHand.Value > 0 ? item.OnHand.Value : 0) : (double?)null;
                if (item.DiscountRatio == 0) continue;
                if (item.DiscountRatio < 0)
                {
                    item.DiscountRatio = 0;
                }
                else if (item.DiscountRatio > 100)
                {
                    item.DiscountRatio = 100;
                }
            }
            return result;
        }
        
        public async Task<object> Delete(VoidOrder req)
        {
            if (!Setting.SellAllowOrder)
            {
                throw new KvValidateException("Thiết lập “Cho phép đặt hàng” đang không được bật");
            }
          
            var order = await OrderService.VoidSaleAsync(req.Id, req.IsVoidPayment);

            #region Log

            var log = new AuditTrailLog
            {
                FunctionId = (int)FunctionType.Order,
                Action = (int)AuditTrailAction.Reject,
                BranchId = order.BranchId,
                Content = $"[PublicAPI] Hủy đơn đặt hàng: [OrderCode]{order.Code}[/OrderCode]"
            };
            var rs = await AuditTrailService.AddLog(log);

            if (!rs || !req.IsVoidPayment || order.Payments.Count <= 0)
                return new { Message = KVMessage._GlobalDeleteSuccess };
            foreach (var item in order.Payments)
            {
                var logPayment = new AuditTrailLog()
                {
                    FunctionId = (int)FunctionType.Payment,
                    Action = (int)AuditTrailAction.Reject,
                    BranchId = order.BranchId,
                    Content =
                        $"[PublicAPI] Hủy phiếu thu: {item.Code}, cho đơn đặt hàng: [OrderCode]{order.Code}[/OrderCode], với giá trị: {NormallizeWfp((double)item.Amount)}"
                };
                await AuditTrailService.AddLog(logPayment);
            }

            #endregion

            return new { Message = KVMessage._GlobalDeleteSuccess };
        }

        private async Task<PartnerDelivery> ValidateAndCreateNewPartnerDelivery(InvoiceDeliveryDTO invoiceDelivery)
        {
            var oldPartnerDelivery =
                    await PartnerDeliveryService.GetAll()
                        .FirstOrDefaultAsync(
                            x =>
                                (!string.IsNullOrEmpty(invoiceDelivery.PartnerDelivery.Code) && x.Code == invoiceDelivery.PartnerDelivery.Code) ||
                                (!string.IsNullOrEmpty(invoiceDelivery.PartnerDelivery.ContactNumber) && x.ContactNumber.Equals(invoiceDelivery.PartnerDelivery.ContactNumber)) ||
                                (!string.IsNullOrEmpty(invoiceDelivery.PartnerDelivery.Name) && x.Name.Equals(invoiceDelivery.PartnerDelivery.Name)));
            if (oldPartnerDelivery != null)
            {
                return oldPartnerDelivery;
            }
            var newPartnerDelivery = await PartnerDeliveryService.CreateOrUpdateAsync(invoiceDelivery.PartnerDelivery.ConvertTo<PartnerDelivery>());
            return newPartnerDelivery;
        }
        private async Task<Customer> ValidateAndReturnCustomer(CustomerDTO customer, int branchId)
        {
            if (branchId <= 0)
            {
                throw new KvValidateOrderException(KVMessage.EmptyRetailerBranch);
            }

            // validate customer
            if (customer.Id < -0.0000001)
            {
                throw new KvValidateException("Thông tin khách hàng không hợp lệ");
            }
            if (customer.Id > 0 && !(await CustomerService.GetAll().AnyAsync(x => x.Id == customer.Id)))
            {
                throw new KvValidateException("Khách hàng không tồn tại");
            }

            //check and create new customer
            var queryCustomer = CustomerService.GetAll().Where(x => !Setting.ManagerCustomerByBranch ||
                                                            (Setting.ManagerCustomerByBranch && x.BranchId.HasValue &&
                                                             x.BranchId.Value == branchId));
            if (!string.IsNullOrEmpty(customer.Code))
            {
                var olCustomer = await queryCustomer.Where(p => p.Code == customer.Code).FirstOrDefaultAsync();
                if (olCustomer != null) return olCustomer;
            }
            if (!string.IsNullOrEmpty(customer.ContactNumber))
            {
                var olCustomer = await queryCustomer.Where(p => p.ContactNumber.Equals(customer.ContactNumber)).FirstOrDefaultAsync();
                if (olCustomer != null) return olCustomer;
            }
            if (!string.IsNullOrEmpty(customer.Email))
            {
                var olCustomer = await queryCustomer.Where(p => p.Email.Equals(customer.Email)).FirstOrDefaultAsync();
                if (olCustomer != null) return olCustomer;
            }
            customer.BranchId = customer.BranchId ?? branchId;
            var newCustomer = await CustomerService.CreateOrUpdateAsync(customer.ConvertTo<Customer>(), true);
            return newCustomer;
        }

         
        private async Task<object> WriteLog(OrderDTO order, Dictionary<long, string> products, Order result, Order existingOrder, Invoice invoice)
        {
            var productDetail = "";
            //var customerCode = !string.IsNullOrEmpty(result.Customer?.Code) ? $", khách hàng [CustomerCode]{result.Customer.Code}[/CustomerCode]" : "";
            if (order.OrderDetails != null && order.OrderDetails.Any())
            {
                productDetail = ", bao gồm:<div>";
                foreach (var item in order.OrderDetails)
                {
                    if (string.IsNullOrEmpty(item.ProductCode))
                    {
                        item.ProductCode = products[item.ProductId];
                    }
                    var priceAfterDiscount = item.Price - item.Discount;
                    if (priceAfterDiscount != null)
                        productDetail += $"- [ProductCode]{item.ProductCode}[/ProductCode] : {Normallize(item.Quantity)}*{NormallizeWfp((double)priceAfterDiscount)}<br>";
                }
                productDetail += "</div>";
            }
            var rs = false;
            if (result.Id > 0)
            {
                string action;
                string updatePurchaseDate;
                var updateUser = string.Empty;
                string updateStatus;
                if (existingOrder != null)
                {
                    action = "Cập nhật thông tin";
                    updatePurchaseDate = existingOrder.PurchaseDate != result.PurchaseDate ? $", thời gian {DateFormat(existingOrder.PurchaseDate)}->{DateFormat(result.PurchaseDate)}" : string.Empty;
                    if (existingOrder.SoldById > 0 && existingOrder.SoldById != result.SoldById)
                    {
                        var users = await UserService.GetUsers().Where(x => x.Id == result.SoldById || x.Id == existingOrder.SoldById).ToDictionaryAsync(x => x.Id, y => y.GivenName);
                        var oldUser = existingOrder.SoldById.HasValue && users.ContainsKey(existingOrder.SoldById.Value) ? users[existingOrder.SoldById.Value] : string.Empty;
                        var newUser = result.SoldById.HasValue && users.ContainsKey(result.SoldById.Value) ? users[result.SoldById.Value] : string.Empty;
                        updateUser = $", người bán: {oldUser}->{newUser}";
                    }
                    updateStatus = existingOrder.Status != result.Status ? $"({EnumHelper.ToDescription((OrderState)existingOrder.Status)}->{EnumHelper.ToDescription((OrderState)result.Status)})" : string.Empty;
                }
                else
                {
                    action = "Thêm mới";
                    updatePurchaseDate = $", thời gian {DateFormat(result.PurchaseDate)}";
                    updateUser = $", người bán: {(await UserService.GetCurrentUserAsync(result.SoldById ?? result.CashierId.Value)).GivenName}";
                    updateStatus = $"({EnumHelper.ToDescription((OrderState)result.Status)})";
                }
                var log = new EventMessage
                {
                    FunctionId = (int)FunctionType.Order,
                    Action = existingOrder != null ? (int)AuditTrailAction.Update : (int)AuditTrailAction.Create,
                    BranchId = result.BranchId,
                    Content = $"[PublicAPI] {action} đơn đặt hàng: [OrderCode]{result.Code}[/OrderCode]{updateStatus}{updatePurchaseDate}{updateUser}{productDetail}"
                };
                if (existingOrder == null)
                {
                    var branch = result.Branch ?? await BranchService.GetByIdAsync(result.BranchId);
                    var soldBy = result.Seller ?? await UserService.GetCurrentUserAsync(result.SoldById ?? CurrentUser.Id);
                    log.DocumentId = result.Id;
                    log.DocumentCode = result.Code;
                    log.ActionByUserId = result.SoldById ?? CurrentUser.Id;
                    log.ActionByUserName = soldBy?.GivenName;
                    log.BranchId = result.BranchId;
                    log.BranchName = branch?.Name;
                    log.RetailerId = result.RetailerId;
                    log.EventType = Order._Create;
                    log.Value = result.Total;
                    log.Status = (int)NotificationState.New;
                    log.IsSystem = false;
                    log.NotifyMessage = $"{log.ActionByUserName} nhận đặt hàng với giá trị {NormallizeWfp((double)result.Total)} tại {branch.Name}";
                }
                var res = (dynamic)await EventMessageService.SendMessage(log);
                if (res.NotificationSuccess && invoice != null)
                {
                    var notifyInvoice = new EventMessage
                    {                                                                                                                                                                                                                                                                                                  // Notification
                        DocumentId = invoice.Id,
                        DocumentCode = invoice.Code,
                        ActionByUserId = invoice.SoldById,
                        ActionByUserName = log.ActionByUserName,
                        BranchId = invoice.BranchId,
                        BranchName = log.BranchName,
                        RetailerId = invoice.RetailerId,
                        EventType = Invoice._Create,
                        Value = invoice.Total,
                        Status = (int)NotificationState.New,
                        IsSystem = false,
                        NotifyMessage = $"{log.ActionByUserName} bán đơn hàng với giá trị {NormallizeWfp((double)invoice.Total)} tại {log.BranchName}"
                    };
                    await EventMessageService.SendMessage(notifyInvoice);
                }
                rs = res.AuditTrailSuccess;
            }
            var lstSurchargeDetails = result.InvoiceOrderSurcharges;
            if (lstSurchargeDetails != null && lstSurchargeDetails.Count > 0)
            {
                var customerCode = !string.IsNullOrEmpty(result.Customer?.Code) ? result.Customer.Code : "";
                var surchargeInfo = ", bao gồm:<div>";
                foreach (var item in result.InvoiceOrderSurcharges)
                {
                    var existId = result.InvoiceOrderSurcharges.Select(s => s.SurchargeId).Distinct();
                    var lstSurchages = await SurchargeService.GetAll().Where(x => existId.Contains(x.Id)).ToListAsync();
                    var surchargeItem = lstSurchages.FirstOrDefault(s => s.Id == item.SurchargeId);
                    if (surchargeItem != null)
                        surchargeInfo += $"- Mã: [SurchargeCode]{surchargeItem.Code}[/SurchargeCode], loại thu: {surchargeItem.Name}, với giá trị: {NormallizeWfp((double)item.Price)}<br>";
                }
                surchargeInfo += "</div>";
                var logSurcharge = new AuditTrailLog
                {
                    FunctionId = (int)FunctionType.Surcharge,
                    Action = (int)AuditTrailAction.Create,
                    BranchId = result.BranchId,
                    Content = $"Thông tin thu khác cho đơn đặt hàng: [OrderCode]{result.Code}[/OrderCode]{customerCode}, với giá trị: {NormallizeWfp((double)(result.Surcharge ?? 0))}, thời gian: {DateFormat(result.PurchaseDate)}{surchargeInfo}"
                };
                await AuditTrailService.AddLog(logSurcharge);
            }
            if (!rs || result.LogPayments == null || result.LogPayments.Count <= 0) return true;
            {
                foreach (var item in result.LogPayments)
                {
                    var customerCode = !string.IsNullOrEmpty(item.CustomerCode) ? $", khách hàng: {item.CustomerCode}"
                        : "";
                    var logPayment = new AuditTrailLog
                    {
                        FunctionId = (int)FunctionType.Payment,
                        Action = (int)AuditTrailAction.Create,
                        BranchId = result.BranchId,
                    Content =
                            $"[PublicAPI] Tạo phiếu thu: [PaymentCode]{item.Code}[/PaymentCode], cho đơn đặt hàng: [OrderCode]{item.Order.Code}[/OrderCode][CustomerCode]{customerCode}[/CustomerCode], với giá trị: {NormallizeWfp((double)item.Amount)}, phương thức thanh toán: {EnumHelper.ToDisplayText<PaymentType>(item.Method)}, thời gian: {DateFormat(item.TransDate)}"
                    };

                    await AuditTrailService.AddLog(logPayment);
                }
            }
            
            return true;
        }

        [CheckNewFnbFilter]
        public async Task<object> Post(OrderRequestDTO orderRequest)
        {
            if (orderRequest == null) throw new KvValidateException(KVMessage.OrderNotFound);

            #region Set RetailerId

            orderRequest.RetailerId = CurrentRetailerId;

            #endregion

            #region BranchId validate

            var branchesID = await BranchService.GetByRetailer(CurrentRetailerId).Select(x => x.Id).ToListAsync();
            if (!branchesID.Contains(orderRequest.BranchId))
            {
                throw new KvException($"Chi nhánh { orderRequest.BranchId } không tồn tại.");
            }

            #endregion

            #region SoldById validate

            orderRequest.SoldById = CurrentUser != null ? (long?)CurrentUser.Id : null;

            #endregion

            #region CustomerId validate

            orderRequest.CustomerId = orderRequest.CustomerId > 0 ? orderRequest.CustomerId : null;

            if (orderRequest.CustomerId != null && !(await CustomerService.GetAll().Where(c => c.RetailerId == orderRequest.RetailerId && c.Id == (long)orderRequest.CustomerId).AnyAsync()))
            {
                throw new KvException($"Khách hàng { orderRequest.CustomerId } không tồn tại hoặc không thuộc Retailer {orderRequest.RetailerId}");
            }

            #endregion

            #region Set DiningOption By Setting
            var UsingCod = 0;
            if (Setting.UseCod)
            {
                UsingCod = 1;
                orderRequest.DiningOption = (int)DiningOption.Delivery;
            }
            else
            {
                UsingCod = 0;
                orderRequest.DiningOption = (int)DiningOption.TakeAway;
            }
            #endregion

            #region Set PurchaseDate

            orderRequest.PurchaseDate = DateTime.Now;

            #endregion

            #region General Extra

            dynamic method = new ExpandoObject();
            method.Id = "Cash";
            method.Label = "Tiền mặt";

            var extra = new ServiceModel.OrderExtraJson
            {
                Amount = null,
                Method = method,
                PriceBookId = new PriceBook
                {
                    Id = -1,
                    Name = "Bảng giá chung"
                },
                EntryDate = DateTime.Now,
                GroupedTableIds = null
            };

            orderRequest.Extra = JsonConvert.SerializeObject(extra);

            #endregion

            #region Xử lý khi sản phẩm là dịch vụ tính giờ

            var products = await ProductService.GetByIdsAsync(orderRequest.OrderDetails.Select(x => x.ProductId).ToList());
            foreach (var orderDetail in orderRequest.OrderDetails)
            {
                var p = products.FirstOrDefault(x => x.Id == orderDetail.ProductId);
                if (p.ProductType == 3 && p.IsTimeType == true)
                {
                    orderDetail.IsStartTimeCounter = false;
                    orderDetail.TimeStartCounter = orderRequest.PurchaseDate;
                    orderDetail.TimeEndCounter = orderDetail.TimeStartCounter;
                }
            }

            #endregion

            #region General Guid for orderDetail,order

            orderRequest.Uuid = Guid.NewGuid().ToString();
            foreach (var orderDetail in orderRequest.OrderDetails)
            {
                orderDetail.Uuid = Guid.NewGuid().ToString();
                orderDetail.Price = orderDetail.Price > 0 ? orderDetail.Price : 0;
            }

            #endregion

            #region Map dataRequest

            var orderDetails = orderRequest.OrderDetails.Select(item => item.ConvertTo<OrderDetail>()).ToList();
            var orderToInsert = orderRequest.ConvertTo<Order>();
            orderToInsert.OrderDetails = orderDetails;
            orderToInsert.UsingCod = (byte?)UsingCod;

            #endregion

            #region Validate PartnerDelivery
            PartnerDelivery deliveryExist = null;
            if (orderRequest.DeliveryDetail != null && orderRequest.DeliveryDetail.PartnerDelivery != null)
            {
                if (orderRequest.DeliveryDetail.PartnerDelivery.Code != null
                    && (deliveryExist = await PartnerDeliveryService.GetAll().FirstOrDefaultAsync(x => x.Code == orderRequest.DeliveryDetail.PartnerDelivery.Code)) != null)
                {
                    orderToInsert.DeliveryDetail.DeliveryBy = deliveryExist.Id;
                    orderToInsert.DeliveryDetail.PartnerDelivery = deliveryExist;
                }
                else
                {
                    var partnerDeliveryNew = await CreatePartnerDeliveryAsync(orderRequest.DeliveryDetail.PartnerDelivery);
                    if (partnerDeliveryNew != null)
                    {
                        orderToInsert.DeliveryDetail.DeliveryBy = partnerDeliveryNew.Id;
                        orderToInsert.DeliveryDetail.PartnerDelivery = partnerDeliveryNew;
                    }
                }
            }

            if ((orderToInsert.DeliveryDetail.Status == 0) || (orderToInsert.DeliveryDetail.Status != 3 && orderToInsert.DeliveryDetail.Status != 4))
            {
                orderToInsert.DeliveryDetail.Status = 3;
            }

            #endregion

            #region Set HistoryNote

            await OrderService.WriteKitchenNotifyHistoryLog(orderToInsert, null, string.Empty);

            #endregion
            var result = await OrderService.CreateOrUpdateOrderAsync(orderToInsert);

            #region update Total,TotalPayment,Surcharge

            var newOrder = await OrderService.GetAll().AsNoTracking().FirstOrDefaultAsync(o => o.Id == result.Id);
            if (newOrder != null)
            {
                result.Total = newOrder.Total;
                result.TotalPayment = newOrder.TotalPayment;
                result.Surcharge = newOrder.Surcharge;
            }

            #endregion

            #region Write log

            await WriteLogCreateOrUpdateOrder(result, null, string.Empty);

            #endregion

            #region Notify


            await NotifyKitchenToEventMessageService(orderRequest, result);
            await NotifyKitchenToOrderRealTimeService(result);

            var printDocuments = await OrderService.GetPrintDocument(result, (Order)null);

            if (printDocuments != null && printDocuments.Any() && CurrentUser != null)
            {
                foreach (var pd in printDocuments)
                {
                    pd.OrderBy = !string.IsNullOrWhiteSpace(CurrentUser.GivenName) ? CurrentUser.GivenName : (await UserService.GetByIdAsync(CurrentUser.Id)).GivenName;
                }
            }

            // Print destroy first
            if (printDocuments != null && printDocuments.Any(d => d.PrintType == (byte)PrintMessageType.Destroy))
            {
                await PrinterRealTimeService.NotifyPrinter(printDocuments.FirstOrDefault(d => d.PrintType == (byte)PrintMessageType.Destroy));
            }
            // Print kitchen
            if (printDocuments != null && printDocuments.Any(d => d.PrintType == (byte)PrintMessageType.Kitchen))
            {
                await PrinterRealTimeService.NotifyPrinter(printDocuments.FirstOrDefault(d => d.PrintType == (byte)PrintMessageType.Kitchen));
            }
            #endregion

            #region AuditLog
            var sourceOrderCopy = OrderService.DetachByClone(result, new[] { "OrderDetails" });
            await WriteLogCreateOrder(sourceOrderCopy);
            #endregion

            #region Map data response

            var OrderDetailsReturn = result.OrderDetails.Select(x => x.ConvertTo<OrderDetailResponseDTO>()).ToList();
            var DeliveryDetailReturn = result.DeliveryDetail.ConvertTo<OrderDeliveryResponseDTO>();
            DeliveryDetailReturn.Status = result.DeliveryDetail.Status;
           // DeliveryDetailReturn.PartnerDelivery = result.DeliveryDetail.PartnerDelivery.ConvertTo<PartnerDeliveryDTO>();
            var orderReturn = result.ConvertTo<OrderResponseDTO>();
            orderReturn.OrderDelivery = DeliveryDetailReturn;
            orderReturn.OrderDetails = OrderDetailsReturn;
            #endregion

            return orderReturn;
        }

        public async Task<object> Post(CreateOrderRequestDto orderRequest)
        {
            if (orderRequest == null) throw new KvValidateException(KVMessage.OrderNotFound);

            #region Set RetailerId
            orderRequest.RetailerId = CurrentRetailerId;
            #endregion

            #region BranchId validate

            var branchesID = await BranchService.GetByRetailer(CurrentRetailerId).Select(x => x.Id).ToListAsync();
            if (!branchesID.Contains(orderRequest.BranchId))
            {
                throw new KvException($"Chi nhánh {orderRequest.BranchId} không tồn tại.");
            }

            #endregion

            #region SoldById validate

            orderRequest.SoldById = CurrentUser != null ? (long?)CurrentUser.Id : null;

            #endregion

            #region CustomerId validate

            await ValidateCustomer(orderRequest);

            #endregion

            #region Set DiningOption By Setting
            var UsingCod = 0;
            if (Setting.UseCod)
            {
                UsingCod = 1;
                orderRequest.DiningOption = (int)DiningOption.Delivery;
            }
            else
            {
                UsingCod = 0;
                orderRequest.DiningOption = (int)DiningOption.TakeAway;
            }
            #endregion

            #region Set PurchaseDate

            orderRequest.PurchaseDate = DateTime.Now;

            #endregion

            #region General Extra

            dynamic method = new ExpandoObject();
            method.Id = "Cash";
            method.Label = "Tiền mặt";

            var extra = new ServiceModel.OrderExtraJson
            {
                Amount = null,
                Method = method,
                PriceBookId = new PriceBook
                {
                    Id = -1,
                    Name = "Bảng giá chung"
                },
                EntryDate = DateTime.Now,
                GroupedTableIds = null
            };

            orderRequest.Extra = JsonConvert.SerializeObject(extra);

            #endregion

            #region Xử lý khi sản phẩm là dịch vụ tính giờ

            var products = await ProductService.GetByIdsAsync(orderRequest.OrderDetails.Select(x => x.ProductId).ToList());
            foreach (var orderDetail in orderRequest.OrderDetails)
            {
                var p = products.FirstOrDefault(x => x.Id == orderDetail.ProductId);
                if (p != null && p.ProductType == 3 && p.IsTimeType == true)
                {
                    orderDetail.IsStartTimeCounter = false;
                    orderDetail.TimeStartCounter = orderRequest.PurchaseDate;
                    orderDetail.TimeEndCounter = orderDetail.TimeStartCounter;
                }
            }

            #endregion

            #region General Guid for orderDetail,order

            orderRequest.Uuid = Guid.NewGuid().ToString();
            foreach (var orderDetail in orderRequest.OrderDetails)
            {
                orderDetail.Uuid = Guid.NewGuid().ToString();
                orderDetail.Price = orderDetail.Price > 0 ? orderDetail.Price : 0;
            }

            #endregion

            #region Map dataRequest

            var orderDetails = orderRequest.OrderDetails.Select(item => item.ConvertTo<OrderDetail>()).ToList();
            var orderToInsert = orderRequest.ConvertTo<Order>();
            orderToInsert.OrderDetails = orderDetails;
            orderToInsert.UsingCod = (byte?)UsingCod;
            #endregion

            #region Validate PartnerDelivery
           
            await ValidatePartnerDelivery(orderRequest, orderToInsert);

            #endregion

            #region Set HistoryNote
            var deliveryStatus = orderToInsert.DeliveryDetail?.Status ?? 3;
            orderToInsert = OrderService.DetachByClone(orderToInsert, new string[] { "DeliveryDetail", "DeliveryDetail.PartnerDelivery", "DeliveryDetail.Status", "OrderDetails" });
            if (orderToInsert.DeliveryDetail != null)
            {
                orderToInsert.DeliveryDetail.Status = deliveryStatus;
            }

            #endregion

            #region Set AddressLocation
            var administrativeAreaId = orderRequest.DeliveryDetail.AdministrativeAreaId;
            orderToInsert.DeliveryDetail.AddressLocation.AdministrativeAreaId = administrativeAreaId;

            #endregion

            var command = new PublicApiSubmitOrderCommand(
                "KiotViet FnB Public Api",
                AuthService.Context.RetailerCode,
                AuthService.Context.RetailerId, 
                orderToInsert.BranchId,
                AuthService.Context.GroupId,
                AuthService.Context.User.Id,
                orderToInsert
                );

            var topic = AppSettings.Get<CommandDispatcher.Kafka.Producer.ProducerConfig>("ordering.command.bus").TopicV2;
            CommandDispatcher.Send(command, topic);

            return orderToInsert.Uuid;
        }

        private async Task ValidatePartnerDelivery(CreateOrderRequestDto orderRequest, Order orderToInsert)
        {
            PartnerDelivery deliveryExist = null;
            if (orderToInsert.DeliveryDetail == null)
                return;
            if (orderRequest.DeliveryDetail.PartnerDelivery != null)
            {
                if (orderRequest.DeliveryDetail.PartnerDelivery.Code != null
                    && (deliveryExist = await PartnerDeliveryService.GetAll().FirstOrDefaultAsync(x => x.Code == orderRequest.DeliveryDetail.PartnerDelivery.Code)) != null)
                {
                    orderToInsert.DeliveryDetail.DeliveryBy = deliveryExist.Id;
                    orderToInsert.DeliveryDetail.PartnerDelivery = deliveryExist;
                }
                else
                {
                    var partnerDeliveryNew = await CreatePartnerDeliveryAsync(orderRequest.DeliveryDetail.PartnerDelivery);
                    if (partnerDeliveryNew != null)
                    {
                        orderToInsert.DeliveryDetail.DeliveryBy = partnerDeliveryNew.Id;
                        orderToInsert.DeliveryDetail.PartnerDelivery = partnerDeliveryNew;
                    }
                }
            }

            if ((orderToInsert.DeliveryDetail.Status == 0) || (orderToInsert.DeliveryDetail.Status != 3 && orderToInsert.DeliveryDetail.Status != 4))
            {
                orderToInsert.DeliveryDetail.Status = 3;
            }
        }

        private async Task ValidateCustomer(CreateOrderRequestDto orderRequest)
        {
            orderRequest.CustomerId = orderRequest.CustomerId > 0 ? orderRequest.CustomerId : null;

            if (orderRequest.CustomerId != null && !(await CustomerService.GetAll().Where(c => c.RetailerId == orderRequest.RetailerId && c.Id == (long)orderRequest.CustomerId).AnyAsync()))
            {
                throw new KvException($"Khách hàng {orderRequest.CustomerId} không tồn tại hoặc không thuộc Retailer {orderRequest.RetailerId}");
            }
        }

        private async Task NotifyKitchenToOrderRealTimeService(Order result)
        {
            var payloadNotify = await GetOrderDetail(new OrderGet() { Id = result.Id });
            if (payloadNotify.SaleChannelId == null || payloadNotify.SaleChannelId == 0)
            {
                payloadNotify.SaleChannelId = 0; 
                var defaultSaleChannel = new SaleChannel
                {
                    Img = "fa fas fa-shopping-basket",
                    Name = Labels.sc_directSaleChannel,
                    Id = 0,
                    Position = 0
                };
                payloadNotify.SaleChannel = defaultSaleChannel;
            }
           
            await OrderRealTimeService.NotifyKitchen(payloadNotify, result.Id.ToString(), null);
        }

        private async Task NotifyKitchenToEventMessageService(OrderRequestDTO orderRequest, Order result)
        {
            var updatedOrder = OrderService.DetachByClone(result, new string[] { "OrderDetails", "Customer", "Seller" });
            if (orderRequest.DeliveryDetail != null)
            {
                updatedOrder.DeliveryDetail = InvoiceDeliveryService.DetachByClone(updatedOrder.InvoiceDeliveries.ToList(), new string[] { "PartnerDelivery", "Location" })
                    .FirstOrDefault();
            }

            await EventMessageService.NotifyKitchen(updatedOrder, result.Id.ToString());
        }

        public async Task<OrderResponseDTO> Get(GetOrderByUuid req)
        {
            
            var ret = await OrderService.GetByUuid(CurrentRetailerId, req.Uuid);
            if (ret == null)
            {
                throw new KvValidateException(KVMessage.NotFound);
            }
            var result = OrderService.DetachByClone(ret, new []{ "Customer" });
            if (ret.InvoiceDeliveries != null && ret.InvoiceDeliveries.Count > 0)
            {
                var deliveryInfo = InvoiceDeliveryService.DetachByClone(ret.InvoiceDeliveries.FirstOrDefault(), new[] { "PartnerDelivery", "DeliveryPackage" });
                if (deliveryInfo != null && deliveryInfo.Id > 0)
                {
                    result.DeliveryDetail = deliveryInfo;
                    if (result.DeliveryDetail != null && result.DeliveryDetail.PartnerDelivery != null)
                    {
                        result.DeliveryDetail.PartnerCode = result.DeliveryDetail.PartnerDelivery.Code;
                    }
                }
            }
            if (ret.OrderDetails != null && ret.OrderDetails.Count > 0)
            {
                result.OrderDetails = OrderDetailService.DetachByClone(ret.OrderDetails.ToList(), new[] { "Product" });
            }

            var orderReturn = result.ConvertTo<OrderResponseDTO>();

            if (result.DeliveryDetail != null)
            {
                var DeliveryDetailReturn = result.DeliveryDetail.ConvertTo<OrderDeliveryResponseDTO>();
                DeliveryDetailReturn.Status = result.DeliveryDetail.Status;
                orderReturn.OrderDelivery = DeliveryDetailReturn;
            }

            if(result.OrderDetails != null)
            {
                var OrderDetailsReturn = result.OrderDetails.Select(x => x.ConvertTo<OrderDetailResponseDTO>()).ToList();
                orderReturn.OrderDetails = OrderDetailsReturn;
            }
            return orderReturn;
        }

        public async Task<Order> GetOrderDetail(OrderGet req)
        {
            if (req.Includes != null)
            {
                var listInclude = req.Includes.ToList();
                req.Includes = listInclude.ToArray();
            }
            var ret = await OrderService.GetByIdAsync(req.Id);
            if (ret == null)
            {
                throw new KvValidateException(KVMessage.NotFound);
            }
            var o = OrderService.DetachByClone(ret, req.Includes);
            if (ret.DeliveryInfoes != null && ret.DeliveryInfoes.Count > 0)
            {
                var deliveryInfo = DeliveryInfoService.DetachByClone(ret.DeliveryInfoes.FirstOrDefault(t => t.RetailerId == CurrentRetailerId && t.IsCurrent == true), new[] { "PartnerDelivery", "DeliveryPackage" });
                if (deliveryInfo != null && deliveryInfo.Id > 0) 
                {
                    o.DeliveryDetail = await InvoiceDeliveryService.ConvertToInvoiceDelivery(deliveryInfo);
                    if (o.DeliveryDetail != null && o.DeliveryDetail.PartnerDelivery != null)
                    {
                        o.DeliveryDetail.PartnerCode = o.DeliveryDetail.PartnerDelivery.Code;
                    }
                }
            }
            if (ret.OrderDetails != null && ret.OrderDetails.Count > 0)
            {
                o.OrderDetails = OrderDetailService.DetachByClone(ret.OrderDetails.ToList(), new[] { "Product" });
            }
            if (ret.InvoiceOrderSurcharges != null && ret.InvoiceOrderSurcharges.Count > 0)
            {
                o.InvoiceOrderSurcharges = InvoiceOrderSurchargeService.DetachByClone(ret.InvoiceOrderSurcharges.ToList(), new[] { "SurchargeName", "ProductSName" });
            }
            if (o.CustomerId != null)
            {
                ret = await OrderService.GetCustomerDebt(ret, req.PaymentId);
                o.CustomerDebt = ret.CustomerDebt;
                o.CustomerOldDebt = ret.CustomerOldDebt;
                if (o.Customer != null)
                {
                    var str = string.Empty;
                    var group = CustomerGroupService.GetAll()
                                .SelectMany(c => c.CustomerGroupDetails.Where(a => a.CustomerId == ret.CustomerId).Select(b => b.CustomerGroup.Name)).ToList();
                    o.Customer.Groups = group.Aggregate(str, (current, customerGroup) => current + (customerGroup + ", "));
                    o.Customer.Groups = o.Customer.Groups.Trim(new char[] { ',', ' ' });
                }
            }

            return o;
        }

        private async Task<ValidResult> ValidOrder(Order obj, bool isNewOrder, bool isCheckItemBeforePurchase = false)
        {
            var retVal = new ValidResult { Order = obj, Message = string.Empty };
            if (obj?.OrderDetails == null) return retVal;
            var dic = obj.OrderDetails.ToDictionary(a => a.Uuid);
            var invoice = await OrderService.GetByIdAsync(obj.Id);
            if (invoice == null)
            {
                if (!isNewOrder)
                    retVal.Message = "Đơn hàng không tồn tại.";
                return retVal;
            }

            foreach (var detail in invoice.OrderDetails)
            {
                if (dic.ContainsKey(detail.Uuid))
                {
                    var des = dic[detail.Uuid];
                    if ((des.Quantity < (detail.ProcessingQty ?? 0) || des.Quantity < (detail.DeliveryQty ?? 0)) && AuthService.CheckPermission(TableAndRoom.DenyRemoveItem)
                                                                                                                 && !isCheckItemBeforePurchase)
                    {
                        retVal.Message = "Bạn không được phép giảm số lượng hàng nhỏ hơn số lượng đã chế biến, vui lòng liên hệ Thu ngân để thực hiện";
                        retVal.Order = invoice;
                    }

                    if (!AuthService.CheckPermission(TableAndRoom.DenyRemoveItem) || CurrentUser.IsAdmin || isCheckItemBeforePurchase) continue;
                    if (des.Quantity >= detail.Quantity) continue;
                    retVal.Message = "Bạn không được phép giảm số lượng hàng nhỏ hơn số lượng đã thông báo nhà bếp";
                    break;
                }
                if (!AuthService.CheckPermission(TableAndRoom.DenyRemoveItem) || CurrentUser.IsAdmin || detail.ToppingParentUuid != null || isCheckItemBeforePurchase) continue;
                retVal.Message = "Bạn không được phép xóa món đã thông báo nhà bếp";
                break;
            }
            return retVal;
        }

        public async Task<string> GenerateTableLog(int tableId)
        {
            string rs;
            if (tableId > 0)
            {
                var table = await TableAndRoomService.GetByIdAsync((long)tableId);
                rs = $"{table?.Name}";
                if (table?.GroupId != null)
                {
                    var oldGroup = await TableGroupService.GetByIdAsync((long)table.GroupId);
                    rs += $" - {oldGroup?.Name}";
                }
            }
            else
            {
                rs = tableId == -(byte)DiningOption.Delivery ? $" {Labels.auditLog_giao_di}" : $" {Labels.auditLog_TakeAway}";
            }
            return rs;
        }

        private async Task<string> GenerateOrderLog(Order ord)
        {
            var toTable = await GenerateTableLog(OrderService.GetTableIdByDiningOption(ord.TableId, ord.DiningOption));
            var purchaseDate = $", Thời gian {DateFormat(ord.PurchaseDate)}";
            var logPriceBook = ", Bảng giá: Bảng giá chung";
            if (!string.IsNullOrWhiteSpace(ord.Extra))
            {
                var parseExtra = JsonConvert.DeserializeObject<Persistence.Common.OrderExtraJson>(ord.Extra);
                if (parseExtra?.PriceBookId != null && parseExtra.PriceBookId.Id > 0)
                {
                    logPriceBook = $", Bảng giá: {parseExtra.PriceBookId.Name}";
                }
            }
            var discount = ord.Discount != null && ord.Discount > 0
                ? (ord.DiscountRatio != null && ord.DiscountRatio > 0
                    ? $", Giảm giá: {ord.DiscountRatio} %"
                    : $", Giảm giá: {NormallizeWfp((double)ord.Discount)}")
                : string.Empty;

            var customerCode = ord.CustomerId != null
                ? $", Khách hàng: {(ord.Customer != null ? ord.Customer.Name : ord.CustomerName)}"
                : ", Khách hàng: Khách lẻ";

            var numberCustomer = ord.NumberCustomer != null ? ", SL khách: " + ord.NumberCustomer : "";

            return $"[OrderCode]{ord.Code}[/OrderCode] {toTable} {purchaseDate} {logPriceBook} {discount} {customerCode} {numberCustomer}";
        }

        private async Task WriteLogCreateOrUpdateOrder(Order order, Order existOrder, string isCombineTable)
        {
            try
            {
                var isNewOrder = existOrder != null ? false : true;
                var oldCus = existOrder?.Customer;
                var oldPurchaseDate = new DateTime();
                if (existOrder != null)
                    oldPurchaseDate = existOrder.PurchaseDate;

                var logPriceBook = ", Bảng giá: Bảng giá chung";
                if (!string.IsNullOrWhiteSpace(order.Extra))
                {
                    var parseExtra = JsonConvert.DeserializeObject<Persistence.Common.OrderExtraJson>(order.Extra);
                    if (parseExtra != null && parseExtra.PriceBookId != null && parseExtra.PriceBookId.Id > 0)
                    {
                        logPriceBook = $", Bảng giá: {parseExtra.PriceBookId.Name}";
                    }
                }

                if (order.Id > 0)
                {
                    var productDetail = "";
                    if (order.OrderDetails != null && order.OrderDetails.Any())
                    {
                        productDetail = ", Bao gồm:<div>";
                        foreach (var item in order.OrderDetails)
                        {
                            if (item.ToppingParentUuid == null)
                            {
                                var lsTopping = order.OrderDetails.Where(p => p.ToppingParentUuid == item.Uuid);
                                var subTopping = "topping ";
                                foreach (var idx in lsTopping)
                                {
                                    subTopping += $"[ProductCode]{idx.ProductCode}[/ProductCode] {idx.ProductFullName}: {Normallize((idx.ToppingQty ?? 0) * item.Quantity)}*{NormallizeWfp((double)(idx.Price - idx.Discount))}, ";
                                }
                                subTopping = lsTopping.Any() ? $", {subTopping}" : "";
                                productDetail +=
                                $"- [ProductCode]{item.ProductCode}[/ProductCode] {item.ProductFullName}{subTopping}: {Normallize(item.Quantity)}*{NormallizeWfp((double)(item.Price - item.Discount))}<br>";
                            }
                        }
                        productDetail += "</div>";
                    }

                    var customerCode = "";
                    if (isNewOrder && order.Customer != null)
                    {
                        customerCode = $", Khách hàng: [CustomerCode]{order.Customer.Code}[/CustomerCode]";
                    }
                    else
                    {
                        if (oldCus == null)
                        {
                            if (order.CustomerId != null)
                            {
                                var cus = await CustomerService.GetByIdAsync(order.CustomerId ?? 0);
                                var cusCode = cus != null ? cus.Code : "";
                                customerCode = !string.IsNullOrEmpty(cusCode) ? string.Format(", Khách hàng : khách lẻ -> [CustomerCode]{0}[/CustomerCode]", cusCode) : "";
                            }
                        }
                        else
                        {
                            if (order.CustomerId == null)
                            {
                                customerCode = !string.IsNullOrEmpty(oldCus.Code) ? string.Format(", Khách hàng : [CustomerCode]{0}[/CustomerCode] -> khách lẻ ", oldCus.Code) : "";
                            }
                            else
                            {
                                if (order.CustomerId != oldCus.Id)
                                {
                                    var cus = await CustomerService.GetByIdAsync(order.CustomerId ?? 0);
                                    var cusCode = cus != null ? cus.Code : "";
                                    customerCode = string.Format(", Khách hàng: [CustomerCode]{0}[/CustomerCode] -> [CustomerCode]{1}[/CustomerCode]", oldCus.Code, cus.Code);
                                }
                            }
                        }
                    }


                    var tableName = await GenerateTableLog(OrderService.GetTableIdByDiningOption(order.TableId, order.DiningOption));

                    var updatePurchaseDate = oldPurchaseDate != order.ComparePurchaseDate ? string.Format(", Thời gian {0}->{1}", DateFormat(oldPurchaseDate), DateFormat(order.ComparePurchaseDate)) : "";
                    if (isNewOrder)
                    {
                        updatePurchaseDate = $", Thời gian {DateFormat(order.ComparePurchaseDate)}";
                    }
                    var updateUser = "";
                    if (order.CompareSoldById > 0 && order.CompareSoldById != order.SoldById)
                    {
                        var user = await UserService.GetCurrentUserAsync(order.SoldById ?? 0);
                        var oldUser = await UserService.GetCurrentUserAsync(order.CompareSoldById);
                        updateUser = $", Người bán: {oldUser.GivenName}->{user.GivenName}";
                    }
                    var updateStatus = "";
                    string contentLog = !isNewOrder ? "Cập nhật thông tin đơn đặt hàng:" : "Tạo đơn đặt hàng:";
                    // AuditTrailLog
                    var log = new AuditTrailLog();
                    var combineTable = isCombineTable;

                    log.FunctionId = (int)FunctionType.Order;
                    log.Content = $"{contentLog} [OrderCode]{order.Code}[/OrderCode] {tableName}{combineTable}{updateStatus}{customerCode}{updatePurchaseDate}{updateUser}{logPriceBook}{productDetail}";
                    //log.Action = order.Id > 0 ? (int)AuditTrailAction.Update : (int)AuditTrailAction.Create;
                    // Khi Click thong bao thif order Id da co nhung status = 1, van o phieu tam
                    log.Action = order.Status == (int)OrderState.Draft && isNewOrder ? (int)AuditTrailAction.Create : (int)AuditTrailAction.Update;
                    await AuditTrailService.AddLog(log);
                }
            }
            catch (Exception ex)
            {
                Log.Error("Write order audit log error", ex);
            }
        }

        private async Task WriteLogRemoveItemWhenCheckItemBeforePurchase(Order order, Order existing)
        {
            try
            {
                string contentLog = "Hủy/Giảm số lượng:";
                var log = new AuditTrailLog();
                if (order != null && order.Id > 0 && existing != null)
                {
                    string productDetail = string.Empty;
                    bool isReduceQuantity = false;
                    if (order.OrderDetails != null && existing.OrderDetails != null && existing.OrderDetails.Any())
                    {
                        productDetail = ", Bao gồm:<div>";
                        var newDetail = order.OrderDetails.ToDictionary(a => a.Uuid);
                        foreach (var detail in existing.OrderDetails)
                        {
                            if (newDetail.ContainsKey(detail.Uuid))
                            {
                                var item = newDetail[detail.Uuid];
                                double delta = detail.Quantity - (item != null ? item.Quantity : 0);
                                if (delta > 0)
                                {
                                    productDetail += $"- [ProductCode]{item.ProductCode}[/ProductCode] {item.ProductFullName} : {Normallize(delta)}*{NormallizeWfp((double)(item.Price - item.Discount))}<br>";
                                    isReduceQuantity = true;
                                }
                            }
                            else
                            {
                                productDetail += $"- [ProductCode]{detail.ProductCode}[/ProductCode] {detail.ProductFullName} : {Normallize(detail.Quantity)}*{NormallizeWfp((double)(detail.Price - detail.Discount))}<br>";
                                isReduceQuantity = true;
                            }
                        }
                        productDetail += "</div>";
                    }

                    if (!isReduceQuantity)
                        return;

                    var customerCode = "";
                    if (order.Customer != null)
                    {
                        customerCode = $", Khách hàng: [CustomerCode]{order.Customer.Code}[/CustomerCode]";
                    }

                    var entryDate = "";
                    entryDate = order.EntryDate != null ? $", Giờ đến: {order.EntryDate?.ToString("dd/MM/yyyy HH:mm:ss")}" : "";

                    // need check with dining option
                    var tableName = await GenerateTableLog(OrderService.GetTableIdByDiningOption(order.TableId, order.DiningOption));


                    var totalAmount = $", Tổng tiền: {NormallizeWfp((double)order.Total)}";

                    var reason = $"<br>Lý do hủy/giảm: Kiểm đồ trước thanh toán";

                    // AuditTrailLog
                    log.FunctionId = (int)FunctionType.Order;
                    log.Action = (int)AuditTrailAction.Delete;
                    log.Content = $"{contentLog} trong đơn hàng [OrderCode]{order.Code}[/OrderCode] {tableName}{productDetail}{customerCode}{entryDate}{reason}";
                }
                else
                {
                    return;
                }

                await AuditTrailService.AddLog(log);
            }
            catch (Exception ex)
            {
                Log.Error("Write check items before purchase audit log error", ex);
            }
        }

        public async Task<string> WriteLogTempOrderReplaceNotifiedOrder(Order reqOrder, Order existedOrder, bool forInvoice = false)
        {
            #region existedOrder log
            var logExistedOrder = $"Cập nhật thông tin đơn đặt hàng: {existedOrder.Code}, Cập nhật dữ liệu đơn hàng mới do có trùng lặp đơn như sau: <br />" +
                $"Đơn hàng {existedOrder.Code}" +
                $"{await GetLogTableAndRoomAsync(existedOrder.TableId ?? OrderService.GetTableIdByDiningOption(existedOrder.TableId, existedOrder.DiningOption))}" +
                $", Thời gian {DateFormat(existedOrder.PurchaseDate)}" +
                $"{await GetLogCustomerAsync(existedOrder.CustomerId)}" +
                $"{(existedOrder.NumberCustomer != null ? $", Số khách: {existedOrder.NumberCustomer}" : string.Empty)}" +
                $"{GetLogPriceBook(existedOrder.Extra)}" +
                $"{await GetLogReceiverOrderByAsync(existedOrder.SoldById)}" +
                $"{GetOrderDetailLog(existedOrder)}";
            #endregion

            #region newOrder log
            var logNewOrder = $"{(forInvoice ? $"Hóa đơn {reqOrder.Code}" : "Đơn hàng mới")}{await GetLogTableAndRoomAsync(reqOrder.TableId ?? OrderService.GetTableIdByDiningOption(reqOrder.TableId, reqOrder.DiningOption))}" +
                $", Thời gian {DateFormat(reqOrder.ComparePurchaseDate)}" +
                $"{await GetLogCustomerAsync(reqOrder.CustomerId)}" +
                $"{(reqOrder.NumberCustomer != null ? $", Số khách: {reqOrder.NumberCustomer}" : string.Empty)}" +
                $"{GetLogPriceBook(reqOrder.Extra)}" +
                $"{await GetLogReceiverOrderByAsync(reqOrder.SoldById)}" +
                $"{GetOrderDetailLog(reqOrder)}";
            #endregion

            return $"{logExistedOrder}{logNewOrder}";
        }

        private async Task<string> GetLogTableAndRoomAsync(int tableId)
        {
            var table = tableId > 0 ? await TableAndRoomService.GetAll().FirstOrDefaultAsync(t => t.Id == tableId) : null;
            var tableLog = string.Empty;
            if (table != null)
            {
                tableLog = $" {table.Name}";

                var tableGroup = await TableGroupService.GetByIdAsync(table.GroupId ?? 0);

                if (tableGroup != null)
                {
                    tableLog += $" - {tableGroup.Name}";
                }
            }
            else if (tableId < 0)
            {
                tableLog = tableId == -(byte)DiningOption.Delivery ? $" {Labels.auditLog_giao_di}" : $"{Labels.auditLog_TakeAway}";
            }

            return tableLog;
        }

        private async Task<string> GetLogCustomerAsync(long? customerId)
        {
            var customer = customerId != null ? await CustomerService.GetAll().FirstOrDefaultAsync(c => c.Id == (long)customerId) : null;
            var customerLog = string.Empty;
            if (customer != null)
            {
                customerLog = $", Khách hàng: {customer.Code}";
            }

            return customerLog;
        }

        private string GetLogPriceBook(string extra)
        {
            var pricebookLog = ", Bảng giá: Bảng giá chung";
            if (!string.IsNullOrWhiteSpace(extra))
            {
                var parseExtra = JsonConvert.DeserializeObject<Persistence.Common.OrderExtraJson>(extra);
                if (parseExtra != null && parseExtra.PriceBookId != null && parseExtra.PriceBookId.Id > 0)
                {
                    pricebookLog = $", Bảng giá: {parseExtra.PriceBookId.Name}";
                }
            }

            return pricebookLog;
        }

        private async Task<string> GetLogReceiverOrderByAsync(long? soldById)
        {
            var soldBy = soldById != null ? await UserService.GetCurrentUserAsync((long)soldById) : null;
            var soldByLog = string.Empty;
            if (soldBy != null)
            {
                soldByLog = $", NV nhận đơn: {soldBy.GivenName}";
            }
            return soldByLog;
        }

        private string GetOrderDetailLog(Order order)
        {
            var detailsLog = string.Empty;
            if (order != null && order.OrderDetails.Count > 0)
            {
                detailsLog = ", Bao gồm:<div>";
                foreach (var item in order.OrderDetails)
                {
                    if (item.ToppingParentUuid == null)
                    {
                        var lsTopping = order.OrderDetails.Where(p => p.ToppingParentUuid == item.Uuid);
                        var subTopping = "topping ";
                        foreach (var idx in lsTopping)
                        {
                            subTopping += $"[ProductCode]{idx.ProductCode}[/ProductCode] : {Normallize(idx.ToppingQty ?? 0)}*{NormallizeWfp((double)(idx.Price - idx.Discount))}, ";
                        }
                        subTopping = lsTopping.Any() ? $", {subTopping}" : "";
                        detailsLog +=
                            $"- [ProductCode]{item.ProductCode}[/ProductCode] {subTopping} : {Normallize(item.Quantity)}*{NormallizeWfp((double)(item.Price - item.Discount))}<br>";
                    }
                }
                detailsLog += "</div>";
            }

            return detailsLog;
        }

        private async Task WriteLogCheckItemBeforePurchase(Order order)
        {
            try
            {
                var productDetail = "";
                if (order.OrderDetails != null && order.OrderDetails.Any())
                {
                    productDetail = ", Bao gồm:<div>";
                    foreach (var item in order.OrderDetails)
                    {
                        if (item.ToppingParentUuid == null)
                        {
                            var lsTopping = order.OrderDetails.Where(p => p.ToppingParentUuid == item.Uuid);
                            var subTopping = "topping ";
                            foreach (var idx in lsTopping)
                            {
                                subTopping += $"[ProductCode]{idx.ProductCode}[/ProductCode] {idx.ProductFullName}: {Normallize((idx.ToppingQty ?? 0) * item.Quantity)}*{NormallizeWfp((double)(idx.Price - idx.Discount))}, ";
                            }
                            subTopping = lsTopping.Any() ? $", {subTopping}" : "";
                            productDetail +=
                            $"- [ProductCode]{item.ProductCode}[/ProductCode] {item.ProductFullName}{subTopping}: {Normallize(item.Quantity)}*{NormallizeWfp((double)(item.Price - item.Discount))}<br>";
                        }
                    }
                    productDetail += "</div>";
                }
                var log = new AuditTrailLog();
                log.FunctionId = (int)FunctionType.CheckItemBeforePurchase;
                var tableName = await GenerateTableLog(OrderService.GetTableIdByDiningOption(order.TableId, order.DiningOption));
                log.Content = $"Kiểm đồ cho đơn hàng: [OrderCode]{order.Code}[/OrderCode] {tableName}{productDetail}";

                await AuditTrailService.AddLog(log);
            }
            catch (Exception ex)
            {
                Log.Error("Write check items before purchase audit log error", ex);
            }
        }

        private async Task<string> WriteLogUpdateReservation(Reservation newReservation, Reservation oldReservation)
        {
            var logContent = string.Empty;

            var isChangedStatus = newReservation.Status != oldReservation.Status;
            var isChangedTables = newReservation.ReservationTable.Select(x => x.TableId).Except(oldReservation.ReservationTable.Select(x => x.TableId)).ToList().Count > 0
                             || oldReservation.ReservationTable.Select(x => x.TableId).Except(newReservation.ReservationTable.Select(x => x.TableId)).ToList().Count > 0;

            var logStatus = string.Empty;
            var logTables = string.Empty;

            if (isChangedStatus)
                logStatus = $"Trạng thái: {ReservationService.GetTextReservationStatus(oldReservation.Status)} -> {ReservationService.GetTextReservationStatus(newReservation.Status)}</br>";

            if (isChangedTables)
            {
                var _tableBeforeIds = oldReservation.ReservationTable.Select(x => x.TableId).ToList();
                var _tablesBefore = TableAndRoomService.GetAll().AsNoTracking().Where(x => _tableBeforeIds.Any(id => id == x.Id)).ToList();
                var _tableAfterIds = newReservation.ReservationTable.Select(x => x.TableId).ToList();
                var _tablesAfter = TableAndRoomService.GetAll().AsNoTracking().Where(x => _tableAfterIds.Any(id => id == x.Id)).ToList();
                logTables = $"Phòng/bàn: {string.Join(",", _tablesBefore.Select(x => x.Name).ToList())} -> {string.Join(",", _tablesAfter.Select(x => x.Name).ToList())} </br>";
            }

            if (isChangedStatus || isChangedTables)
            {
                logContent = $"Cập nhật đặt bàn [ReservationCode]{newReservation.Code}[/ReservationCode] {logStatus}: </br>" +
               $"<div>{logTables}</div>";
                var auditTrailLog = new AuditTrailLog()
                {
                    Action = (int)AuditTrailAction.Update,
                    FunctionId = (int)FunctionType.Reservation,
                    Content = logContent
                };

                await AuditTrailService.AddLog(auditTrailLog);
            }


            return logContent;
        }

        private async Task<string> WriteLogCreateOrder(Order order)
        {
            var productDetail = "";
            if (order.OrderDetails.Count > 0)
            {
                productDetail = ", Bao gồm:<div>";
                foreach (var item in order.OrderDetails)
                {
                    var product = await ProductService.GetByIdAsync(item.ProductId);
                    productDetail +=
                        $"- [ProductCode]{product.Code}[/ProductCode] {product.FullName}: {Normallize(item.Quantity)}*{NormallizeWfp((double)(item.Price - item.Discount))}<br>";
                }
                productDetail += "</div>";
            }

            var logPriceBook = ", Bảng giá: Bảng giá chung";
            if (!string.IsNullOrWhiteSpace(order.Extra))
            {
                var parseExtra = JsonConvert.DeserializeObject<Persistence.Common.OrderExtraJson>(order.Extra);
                if (parseExtra != null && parseExtra.PriceBookId != null && parseExtra.PriceBookId.Id > 0)
                {
                    logPriceBook = $", Bảng giá: {parseExtra.PriceBookId.Name}";
                }
            }

            var customerCode = "";
            if (order.Customer != null)
            {
                customerCode = $", Khách hàng: [CustomerCode]{order.Customer.Code}[/CustomerCode]";
            }

            var updatePurchaseDate = $", Thời gian {DateFormat(order.ComparePurchaseDate)}";
            var updateUser = "";
            if (order.CompareSoldById > 0 && order.CompareSoldById != order.SoldById)
            {
                var user = await UserService.GetCurrentUserAsync(order.SoldById ?? 0);
                var oldUser = await UserService.GetCurrentUserAsync(order.CompareSoldById);
                updateUser = $", Người bán: {oldUser.GivenName}->{user.GivenName}";
            }
            var updateStatus = "";
            string contentLog = "Tạo đơn đặt hàng:";
            var log = new AuditTrailLog();

            // AuditTrailLog

            log.FunctionId = (int)FunctionType.Order;
            log.Action = (int)AuditTrailAction.Create;
            log.RetailerId = order.RetailerId;
            log.BranchId = order.BranchId;
            log.Content = $"Public API - {contentLog} [OrderCode]{order.Code}[/OrderCode]{updateStatus}{customerCode}{updatePurchaseDate}{updateUser}{logPriceBook}{productDetail}"; //thientm - use mine
            await AuditTrailService.AddLog(log);
            return log.Content;
        }

        private async Task<PartnerDelivery> CreatePartnerDeliveryAsync(PartnerDeliveryDTO partnerDeliveryDTO)
        {
            if (string.IsNullOrWhiteSpace(partnerDeliveryDTO.Name))
            {
                throw new KvException($"Tên đối tác là bắt buộc.");
            }

            var partnerDeliveryToCreate = partnerDeliveryDTO.ConvertTo<PartnerDelivery>();
            partnerDeliveryToCreate.isActive = true;
            await PartnerDeliveryService.AddAsync(partnerDeliveryToCreate);
            return partnerDeliveryToCreate;
        }
    }
}